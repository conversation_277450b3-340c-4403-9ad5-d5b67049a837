#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试预测逻辑随机性修复效果的脚本
"""

import pandas as pd
import numpy as np
import sys
import os

# 导入主模块
sys.path.append(os.path.dirname(__file__))

# 直接导入需要的类和函数
exec(open('67(3).py').read())

# 现在可以使用 MFTNModel 和 load_config

def test_prediction_consistency():
    """测试预测一致性"""
    print("=" * 60)
    print("测试预测逻辑随机性修复效果")
    print("=" * 60)
    
    # 检查数据文件是否存在
    data_file = "data.xlsx"
    if not os.path.exists(data_file):
        print(f"错误：找不到数据文件 {data_file}")
        print("请确保数据文件存在于当前目录")
        return False
    
    try:
        # 加载数据
        print("1. 加载历史数据...")
        df = pd.read_excel(data_file)
        required_columns = ['期号', '万位', '千位', '百位', '十位', '个位']
        
        if not all(col in df.columns for col in required_columns):
            print(f"错误：数据文件缺少必要的列: {required_columns}")
            return False
        
        # 提取开奖数据
        history_data = df[['万位', '千位', '百位', '十位', '个位']].values
        print(f"   加载了 {len(history_data)} 期历史数据")
        
        # 创建模型实例
        print("2. 初始化预测模型...")
        config = load_config()
        model = MFTNModel(config)
        
        # 训练模型
        print("3. 训练模型...")
        model.fit(history_data)
        
        # 测试预测一致性
        print("4. 测试预测一致性...")
        print("   进行10次连续预测，检查结果是否一致...")
        
        predictions_list = []
        for i in range(10):
            predictions = model.predict_next()
            predictions_list.append(predictions)
            print(f"   测试 {i+1:2d}: {predictions}")
        
        # 检查一致性
        print("\n5. 分析结果...")
        first_prediction = predictions_list[0]
        all_consistent = True
        inconsistent_positions = []
        
        for pos in range(5):
            position_consistent = True
            first_pos_pred = first_prediction[pos]
            
            for i, pred in enumerate(predictions_list[1:], 2):
                if pred[pos] != first_pos_pred:
                    position_consistent = False
                    if pos not in inconsistent_positions:
                        inconsistent_positions.append(pos)
                    print(f"   位置 {pos} 不一致：测试1={first_pos_pred}, 测试{i}={pred[pos]}")
            
            if not position_consistent:
                all_consistent = False
        
        # 输出结果
        print("\n" + "=" * 60)
        if all_consistent:
            print("✓ 测试通过！所有预测结果完全一致")
            print("✓ 随机性问题已成功修复")
            print(f"✓ 一致的预测结果: {first_prediction}")
        else:
            print("✗ 测试失败！预测结果存在不一致")
            print(f"✗ 不一致的位置: {inconsistent_positions}")
            print("✗ 仍存在随机性问题，需要进一步检查")
        
        print("=" * 60)
        return all_consistent
        
    except Exception as e:
        print(f"测试过程中发生错误: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_backtest_consistency():
    """测试回测结果的一致性"""
    print("\n" + "=" * 60)
    print("测试回测结果一致性")
    print("=" * 60)
    
    data_file = "data.xlsx"
    if not os.path.exists(data_file):
        print(f"错误：找不到数据文件 {data_file}")
        return False
    
    try:
        # 加载数据
        df = pd.read_excel(data_file)
        history_data = df[['万位', '千位', '百位', '十位', '个位']].values
        
        # 进行多次回测
        print("进行3次独立回测，检查结果是否一致...")
        backtest_results = []
        
        for test_num in range(3):
            print(f"\n回测 {test_num + 1}:")
            
            # 创建新的模型实例
            config = load_config()
            model = MFTNModel(config)
            
            # 模拟回测过程
            backtest_periods = 5
            total_hits = 0
            total_predictions = 0
            
            for i in range(backtest_periods):
                # 使用前面的数据训练
                train_end = len(history_data) - backtest_periods + i
                train_data = history_data[:train_end]
                actual = history_data[train_end]
                
                # 训练并预测
                model.fit(train_data)
                predictions = model.predict_next()
                
                # 计算命中数
                period_hits = 0
                period_predictions = 0
                for pos in range(5):
                    if actual[pos] in predictions[pos]:
                        period_hits += 1
                    period_predictions += len(predictions[pos])
                
                total_hits += period_hits
                total_predictions += period_predictions
                
                print(f"   期 {i+1}: 预测={predictions}, 实际={actual.tolist()}, 命中={period_hits}/5")
            
            hit_rate = total_hits / (backtest_periods * 5) if backtest_periods > 0 else 0
            backtest_results.append(hit_rate)
            print(f"   回测命中率: {hit_rate:.2%}")
        
        # 检查回测结果一致性
        print(f"\n回测结果对比:")
        for i, rate in enumerate(backtest_results, 1):
            print(f"   回测 {i}: {rate:.2%}")
        
        # 计算差异
        max_diff = max(backtest_results) - min(backtest_results)
        print(f"\n最大差异: {max_diff:.2%}")
        
        if max_diff < 0.01:  # 差异小于1%认为一致
            print("✓ 回测结果一致，随机性问题已修复")
            return True
        else:
            print("✗ 回测结果存在较大差异，可能仍有随机性问题")
            return False
            
    except Exception as e:
        print(f"回测一致性测试中发生错误: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    print("开始测试预测逻辑随机性修复效果...")
    
    # 测试预测一致性
    prediction_ok = test_prediction_consistency()
    
    # 测试回测一致性
    backtest_ok = test_backtest_consistency()
    
    print("\n" + "=" * 60)
    print("总体测试结果:")
    print(f"预测一致性: {'✓ 通过' if prediction_ok else '✗ 失败'}")
    print(f"回测一致性: {'✓ 通过' if backtest_ok else '✗ 失败'}")
    
    if prediction_ok and backtest_ok:
        print("\n🎉 恭喜！随机性问题已成功修复！")
        print("现在预测结果应该是确定性的，回测和实际预测的命中率应该一致。")
    else:
        print("\n⚠️  仍存在问题，需要进一步检查和修复。")
    
    print("=" * 60)

#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试严格时间分割验证功能
"""

import numpy as np
import sys
import os

def test_strict_validation():
    """测试严格时间分割验证功能"""
    print("=" * 60)
    print("测试严格时间分割验证功能")
    print("=" * 60)
    
    try:
        # 导入主程序
        sys.path.append('.')
        with open('67(3).py', 'r', encoding='utf-8') as f:
            exec(f.read())
        
        # 创建测试数据
        print("1. 创建测试数据...")
        np.random.seed(12345)
        test_data = np.random.randint(0, 10, (300, 5))  # 300期数据
        print(f"   测试数据形状: {test_data.shape}")
        
        # 创建参数优化器
        print("\n2. 创建参数优化器...")
        optimizer = ParameterOptimizer(
            data_file="test_data.xlsx",
            target_hit_rate=0.8,
            max_iterations=100,  # 减少迭代次数以加快测试
            population_size=20,  # 减少种群大小
            num_threads=1
        )
        
        # 测试数据分割
        print("\n3. 测试数据分割...")
        train_data, validation_data, test_data_split = optimizer.split_data_by_time(test_data)
        
        print(f"   原始数据: {len(test_data)}期")
        print(f"   训练集: {len(train_data)}期 ({len(train_data)/len(test_data):.1%})")
        print(f"   验证集: {len(validation_data)}期 ({len(validation_data)/len(test_data):.1%})")
        print(f"   测试集: {len(test_data_split)}期 ({len(test_data_split)/len(test_data):.1%})")
        
        # 测试随机基准
        print("\n4. 测试随机基准计算...")
        random_baseline = optimizer.calculate_random_baseline(test_data_split, num_trials=100)
        
        if random_baseline:
            print(f"   随机基准均值: {random_baseline['mean']:.3f} ({random_baseline['mean']*100:.1f}%)")
            print(f"   随机基准标准差: {random_baseline['std']:.3f}")
        
        # 测试参数稳定性分析
        print("\n5. 测试参数稳定性分析...")
        stability_results = optimizer.analyze_parameter_stability(train_data, num_splits=3)
        
        if stability_results:
            print("   参数稳定性分析完成")
            stable_count = sum(1 for result in stability_results.values() if result['cv'] < 0.3)
            total_count = len(stability_results)
            print(f"   稳定参数: {stable_count}/{total_count}")
        
        # 测试简单参数评估
        print("\n6. 测试参数评估...")
        test_params = {
            'alpha': 2.0, 'lambda': 0.1, 'short_weight': 0.3, 'mid_weight': 0.3,
            'long_weight': 0.3, 'co_weight': 0.1, 'hot_threshold': 2.0,
            'cold_threshold': 8.0, 'hot_multiplier': 1.5, 'cold_multiplier': 1.2,
            'window': 40, 'periodicity': 14, 'selection_count': 2
        }
        
        score = optimizer._evaluate_params_on_data(test_params, train_data, 30)
        print(f"   测试参数评估分数: {score:.3f} ({score*100:.1f}%)")
        
        # 测试正则化评分
        print("\n7. 测试正则化评分...")
        regularized_score = optimizer._calculate_regularized_score(test_params, score)
        print(f"   原始分数: {score:.3f}")
        print(f"   正则化分数: {regularized_score:.3f}")
        
        print("\n" + "=" * 60)
        print("✅ 所有基础功能测试通过！")
        print("=" * 60)
        
        # 可选：运行完整的严格验证优化（耗时较长）
        run_full_test = input("\n是否运行完整的严格验证优化测试？(y/N): ").lower().strip()
        
        if run_full_test == 'y':
            print("\n8. 运行完整严格验证优化...")
            print("   注意：这可能需要几分钟时间...")
            
            # 设置进度回调
            def progress_callback(iteration, total, best_score, best_params):
                if iteration % 10 == 0:  # 每10次迭代显示一次
                    print(f"   进度: {iteration}/{total} ({iteration/total:.1%}), 最佳分数: {best_score:.3f}")
                return True
            
            optimizer.set_progress_callback(progress_callback)
            
            # 运行完整优化
            results = optimizer.optimize_with_strict_validation(test_data)
            
            if results:
                print(f"\n完整优化结果:")
                print(f"   训练集分数: {results['train_score']:.3f}")
                print(f"   验证集分数: {results['validation_score']:.3f}")
                print(f"   测试集分数: {results['test_score']:.3f}")
                print(f"   正则化分数: {results['regularized_score']:.3f}")
                
                if results['random_baseline']:
                    baseline = results['random_baseline']['mean']
                    improvement = results['test_score'] - baseline
                    print(f"   相对随机基准提升: {improvement:.3f} ({improvement*100:.1f}%)")
                
                print(f"\n最佳参数:")
                for param_name, value in results['best_params'].items():
                    print(f"   {param_name}: {value:.4f}")
                
                print("\n🎉 完整严格验证优化测试完成！")
            else:
                print("❌ 完整优化测试失败")
        
        return True
        
    except Exception as e:
        print(f"❌ 测试过程中发生错误: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_comparison_with_old_method():
    """比较新旧方法的差异"""
    print("\n" + "=" * 60)
    print("比较严格验证与传统方法的差异")
    print("=" * 60)
    
    print("传统方法问题:")
    print("  ❌ 参数优化时使用了全部数据（包括要预测的期数）")
    print("  ❌ 回测期数较少（30期），容易过拟合")
    print("  ❌ 没有随机基准比较")
    print("  ❌ 没有参数稳定性分析")
    print("  ❌ 没有正则化惩罚")
    
    print("\n严格验证方法改进:")
    print("  ✅ 严格时间分割：训练集、验证集、测试集完全分离")
    print("  ✅ 增加回测期数：从30期增加到150期")
    print("  ✅ 随机基准测试：与随机选择进行比较")
    print("  ✅ 参数稳定性分析：检查参数在不同时期的一致性")
    print("  ✅ 正则化评分：惩罚过于复杂的参数组合")
    print("  ✅ 详细的性能报告：多维度评估模型性能")
    
    print("\n预期效果:")
    print("  📈 更真实的性能评估")
    print("  🎯 更好的泛化能力")
    print("  📊 更可靠的参数选择")
    print("  ⚖️ 更现实的期望管理")

if __name__ == "__main__":
    print("🔧 开始测试严格时间分割验证功能...\n")
    
    # 基础功能测试
    success = test_strict_validation()
    
    # 方法比较说明
    test_comparison_with_old_method()
    
    print("\n" + "=" * 60)
    if success:
        print("🎉 严格验证功能测试完成！")
        print("✅ 新的验证方法已经准备就绪")
        print("📋 建议：在实际使用中，观察测试集性能与随机基准的比较")
        print("⚠️ 注意：如果模型性能不显著优于随机基准，说明预测能力有限")
    else:
        print("❌ 测试失败，请检查代码实现")
    print("=" * 60)

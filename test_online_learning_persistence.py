#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
在线学习持久化功能测试

测试修复后的在线学习系统是否能够：
1. 在程序重启后保持参数状态
2. 自动使用配置文件中的参数初始化
3. 无需重新优化就能使用在线学习
"""

import json
import os
import sys

def test_config_persistence():
    """测试配置文件持久化"""
    print("=" * 60)
    print("测试配置文件持久化功能")
    print("=" * 60)
    
    config_file = "config.json"
    
    # 检查配置文件是否存在
    if os.path.exists(config_file):
        print("✅ 配置文件存在")
        
        try:
            with open(config_file, 'r', encoding='utf-8') as f:
                config = json.load(f)
            
            print(f"✅ 配置文件读取成功")
            
            # 检查必要的模型参数
            model_params = ['alpha', 'lambda', 'short_weight', 'mid_weight', 'long_weight', 
                          'co_weight', 'hot_threshold', 'cold_threshold', 'hot_multiplier', 
                          'cold_multiplier', 'window', 'periodicity', 'selection_count']
            
            missing_params = []
            for param in model_params:
                if param not in config:
                    missing_params.append(param)
            
            if missing_params:
                print(f"❌ 缺少参数: {missing_params}")
            else:
                print("✅ 所有必要参数都存在")
                
            # 显示当前参数值
            print("\n📊 当前参数值:")
            for param in model_params:
                if param in config:
                    print(f"  {param}: {config[param]}")
                    
            # 检查是否是优化过的参数（与默认值不同）
            default_config = {
                'alpha': 2.0,
                'lambda': 0.1,
                'short_weight': 0.1,
                'mid_weight': 0.25,
                'long_weight': 0.4,
                'co_weight': 0.25,
                'hot_threshold': 1.5,
                'cold_threshold': 7.0,
                'hot_multiplier': 1.2,
                'cold_multiplier': 1.0,
                'selection_count': 2,
                'window': 30,
                'periodicity': 14
            }
            
            optimized_params = []
            for param in model_params:
                if param in config and param in default_config:
                    if abs(config[param] - default_config[param]) > 0.001:
                        optimized_params.append(param)
            
            if optimized_params:
                print(f"\n✅ 检测到优化过的参数: {optimized_params}")
                print("💡 这些参数可以用于初始化在线学习系统")
            else:
                print("\n⚠️ 参数值与默认值相同，可能未经过优化")
                
        except Exception as e:
            print(f"❌ 配置文件读取失败: {e}")
    else:
        print("❌ 配置文件不存在")

def test_online_learning_initialization():
    """测试在线学习初始化逻辑"""
    print("\n" + "=" * 60)
    print("测试在线学习初始化逻辑")
    print("=" * 60)
    
    try:
        # 模拟加载配置
        config_file = "config.json"
        if os.path.exists(config_file):
            with open(config_file, 'r', encoding='utf-8') as f:
                config = json.load(f)
            
            # 模拟参数恢复逻辑
            model_param_keys = ['alpha', 'lambda', 'short_weight', 'mid_weight', 'long_weight', 
                              'co_weight', 'hot_threshold', 'cold_threshold', 'hot_multiplier', 
                              'cold_multiplier', 'window', 'periodicity', 'selection_count']
            
            # 检查是否所有必要参数都存在
            has_all_params = all(key in config for key in model_param_keys)
            print(f"所有必要参数存在: {has_all_params}")
            
            if has_all_params:
                # 提取模型参数
                model_params = {k: v for k, v in config.items() if k in model_param_keys}
                print("✅ 成功提取模型参数")
                
                # 检查参数有效性
                valid_params = True
                for key, value in model_params.items():
                    if not isinstance(value, (int, float)):
                        print(f"❌ 参数 {key} 类型无效: {type(value)}")
                        valid_params = False
                    elif value < 0:
                        print(f"❌ 参数 {key} 值无效: {value}")
                        valid_params = False
                
                if valid_params:
                    print("✅ 所有参数值有效")
                    print("✅ 在线学习系统可以使用这些参数初始化")
                    
                    # 显示将要使用的参数
                    print("\n📋 将用于在线学习的参数:")
                    for key, value in model_params.items():
                        print(f"  {key}: {value}")
                else:
                    print("❌ 存在无效参数，无法初始化在线学习")
            else:
                print("❌ 缺少必要参数，无法初始化在线学习")
        else:
            print("❌ 配置文件不存在，无法测试初始化逻辑")
            
    except Exception as e:
        print(f"❌ 测试初始化逻辑失败: {e}")

def test_parameter_priority():
    """测试参数优先级逻辑"""
    print("\n" + "=" * 60)
    print("测试参数优先级逻辑")
    print("=" * 60)
    
    # 模拟不同的参数来源
    best_optimized_params = None  # 模拟没有最佳优化参数
    
    config_file = "config.json"
    config_params = None
    if os.path.exists(config_file):
        try:
            with open(config_file, 'r', encoding='utf-8') as f:
                config = json.load(f)
            model_param_keys = ['alpha', 'lambda', 'short_weight', 'mid_weight', 'long_weight', 
                              'co_weight', 'hot_threshold', 'cold_threshold', 'hot_multiplier', 
                              'cold_multiplier', 'window', 'periodicity', 'selection_count']
            config_params = {k: v for k, v in config.items() if k in model_param_keys}
        except:
            config_params = None
    
    default_params = {
        'alpha': 2.0,
        'lambda': 0.1,
        'short_weight': 0.1,
        'mid_weight': 0.25,
        'long_weight': 0.4,
        'co_weight': 0.25,
        'hot_threshold': 1.5,
        'cold_threshold': 7.0,
        'hot_multiplier': 1.2,
        'cold_multiplier': 1.0,
        'selection_count': 2,
        'window': 30,
        'periodicity': 14
    }
    
    # 模拟参数选择逻辑
    params_to_use = None
    params_source = ""
    
    if best_optimized_params is not None:
        params_to_use = best_optimized_params
        params_source = "最佳优化参数"
    elif config_params:
        params_to_use = config_params
        params_source = "配置文件参数"
    else:
        params_to_use = default_params
        params_source = "默认参数"
    
    print(f"✅ 选择的参数来源: {params_source}")
    print(f"✅ 参数数量: {len(params_to_use)}")
    
    if params_source == "配置文件参数":
        print("💡 程序重启后可以直接使用配置文件参数初始化在线学习")
    elif params_source == "默认参数":
        print("⚠️ 使用默认参数，建议先进行参数优化")

def provide_usage_instructions():
    """提供使用说明"""
    print("\n" + "=" * 60)
    print("修复后的使用说明")
    print("=" * 60)
    
    print("🎯 修复内容:")
    print("1. ✅ 在线学习系统现在可以使用配置文件中的现有参数")
    print("2. ✅ 程序重启后自动恢复最佳参数（如果存在）")
    print("3. ✅ 自动初始化在线学习系统（如果有可用参数）")
    print("4. ✅ 参数来源优先级：最佳优化参数 > 配置文件参数 > 默认参数")
    
    print("\n🚀 使用方法:")
    print("1. 启动程序后，在线学习系统会自动尝试初始化")
    print("2. 如果配置文件中有参数，会自动使用这些参数")
    print("3. 无需重新进行参数优化就可以使用在线学习")
    print("4. 可以直接点击'启用在线学习'开关开始使用")
    
    print("\n💡 提示:")
    print("- 如果想要更好的参数，仍然可以进行参数优化")
    print("- 优化后的参数会自动保存到配置文件")
    print("- 下次启动程序时会自动使用优化后的参数")

if __name__ == "__main__":
    print("在线学习持久化功能测试")
    print("测试时间:", __import__('datetime').datetime.now().strftime("%Y-%m-%d %H:%M:%S"))
    
    test_config_persistence()
    test_online_learning_initialization()
    test_parameter_priority()
    provide_usage_instructions()
    
    print("\n" + "=" * 60)
    print("测试完成！现在可以启动主程序验证修复效果。")
    print("=" * 60)

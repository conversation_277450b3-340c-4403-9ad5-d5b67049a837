#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试评估函数修复
"""

import numpy as np

def test_data_split_calculation():
    """测试数据分割和回测期数计算"""
    print("🔍 测试数据分割和回测期数计算")
    print("=" * 50)
    
    # 模拟不同的数据量情况
    test_cases = [
        {"total": 365, "name": "1年数据"},
        {"total": 500, "name": "500期数据"},
        {"total": 1000, "name": "1000期数据"},
        {"total": 200, "name": "200期数据（较少）"},
        {"total": 100, "name": "100期数据（很少）"}
    ]
    
    for case in test_cases:
        total_periods = case["total"]
        print(f"\n📊 {case['name']} ({total_periods}期):")
        
        # 数据分割
        train_size = int(total_periods * 0.7)
        validation_size = int(total_periods * 0.15)
        test_size = total_periods - train_size - validation_size
        
        print(f"  训练集: {train_size}期 (70%)")
        print(f"  验证集: {validation_size}期 (15%)")
        print(f"  测试集: {test_size}期 (15%)")
        
        # 测试集回测期数计算（修复后的逻辑）
        min_train_for_test = 30
        max_backtest = test_size - min_train_for_test
        desired_backtest = min(30, test_size//3)
        test_backtest_periods = min(desired_backtest, max_backtest)
        
        print(f"  测试集回测期数: {test_backtest_periods}期")
        print(f"  可用训练数据: {min_train_for_test}-{test_size-test_backtest_periods}期")
        
        if test_backtest_periods <= 0:
            print("  ❌ 测试集数据不足，无法进行有效回测")
        else:
            print("  ✅ 测试集数据充足，可以进行回测")

def test_evaluation_logic():
    """测试评估逻辑"""
    print("\n🔍 测试评估逻辑")
    print("=" * 50)
    
    # 模拟测试集数据
    test_data = np.random.randint(0, 10, (97, 5))  # 97期数据，如用户的情况
    backtest_periods = 20  # 回测20期
    min_train_data = 30
    
    print(f"测试数据: {len(test_data)}期")
    print(f"回测期数: {backtest_periods}期")
    print(f"最少训练数据: {min_train_data}期")
    
    # 检查数据是否足够
    if len(test_data) < backtest_periods + min_train_data:
        print(f"❌ 数据不足：需要至少{backtest_periods + min_train_data}期，实际{len(test_data)}期")
        return
    
    print("✅ 数据充足，开始模拟评估...")
    
    total_hits = 0
    valid_tests = 0
    
    # 模拟评估过程
    for i in range(len(test_data) - backtest_periods, len(test_data)):
        train_data_size = i  # 训练数据大小
        
        if train_data_size < min_train_data:
            print(f"跳过第{i}期：训练数据不足({train_data_size}期)")
            continue
        
        valid_tests += 1
        
        # 模拟预测和命中检查（简化版）
        # 假设有25%的命中率
        if np.random.random() < 0.25:
            total_hits += 1
        
        print(f"第{i}期：训练数据{train_data_size}期，{'命中' if total_hits == valid_tests else '未命中'}")
    
    # 计算最终命中率
    if valid_tests > 0:
        hit_rate = total_hits / valid_tests
        print(f"\n📊 评估结果:")
        print(f"  有效测试: {valid_tests}次")
        print(f"  命中次数: {total_hits}次")
        print(f"  命中率: {hit_rate:.3f} ({hit_rate*100:.1f}%)")
        
        if hit_rate > 0:
            print("✅ 评估成功，命中率正常")
        else:
            print("❌ 命中率为0，可能存在问题")
    else:
        print("❌ 没有有效的测试数据")

def analyze_user_case():
    """分析用户的具体情况"""
    print("\n🔍 分析用户情况")
    print("=" * 50)
    
    # 用户的数据情况
    total_data = 365  # 假设1年数据
    train_data = int(total_data * 0.7)  # 255期
    validation_data = int(total_data * 0.15)  # 54期
    test_data = 97  # 用户显示的97期
    
    print(f"用户数据分割:")
    print(f"  训练集: {train_data}期")
    print(f"  验证集: {validation_data}期")
    print(f"  测试集: {test_data}期")
    
    # 分析测试集回测
    min_train = 30
    max_backtest = test_data - min_train  # 97 - 30 = 67
    desired_backtest = min(30, test_data//3)  # min(30, 32) = 30
    actual_backtest = min(desired_backtest, max_backtest)  # min(30, 67) = 30
    
    print(f"\n测试集回测分析:")
    print(f"  期望回测期数: {desired_backtest}期")
    print(f"  最大可回测: {max_backtest}期")
    print(f"  实际回测期数: {actual_backtest}期")
    
    if actual_backtest > 0:
        print("✅ 回测期数充足")
        
        # 分析每次预测的训练数据
        start_idx = test_data - actual_backtest  # 97 - 30 = 67
        end_idx = test_data  # 97
        
        print(f"\n预测期数范围: 第{start_idx}-{end_idx-1}期")
        
        for i in range(start_idx, min(start_idx + 5, end_idx)):  # 只显示前5期
            train_size = i
            print(f"  第{i}期预测：使用前{train_size}期数据训练")
            
            if train_size >= min_train:
                print(f"    ✅ 训练数据充足({train_size}期 >= {min_train}期)")
            else:
                print(f"    ❌ 训练数据不足({train_size}期 < {min_train}期)")
        
        if end_idx - start_idx > 5:
            print(f"  ... (还有{end_idx - start_idx - 5}期)")
    else:
        print("❌ 无法进行回测")

def suggest_solutions():
    """建议解决方案"""
    print("\n💡 解决方案建议")
    print("=" * 50)
    
    print("如果测试集命中率仍为0，可能的原因和解决方案:")
    
    print("\n1. 数据量不足:")
    print("   原因: 测试集太小，无法进行有效回测")
    print("   解决: 增加历史数据量，建议至少500期以上")
    
    print("\n2. 模型参数问题:")
    print("   原因: 优化得到的参数在测试集上无效")
    print("   解决: 调整参数范围，增加正则化")
    
    print("\n3. 过拟合问题:")
    print("   原因: 验证集80%命中率明显过高，严重过拟合")
    print("   解决: 增加数据量，使用更保守的参数")
    
    print("\n4. 评估函数问题:")
    print("   原因: 评估逻辑有bug或异常")
    print("   解决: 检查控制台错误信息，修复评估函数")
    
    print("\n5. 数据质量问题:")
    print("   原因: 测试集数据与训练/验证集差异太大")
    print("   解决: 检查数据一致性，确保时间连续性")

if __name__ == "__main__":
    print("🔧 测试集命中率为0问题诊断工具")
    print("=" * 60)
    
    try:
        test_data_split_calculation()
        test_evaluation_logic()
        analyze_user_case()
        suggest_solutions()
        
        print("\n🎯 总结:")
        print("1. 修复后的评估函数应该能正确处理小数据集")
        print("2. 增加了详细的调试信息")
        print("3. 使用有效测试次数而不是回测期数计算命中率")
        print("4. 如果问题仍然存在，建议增加数据量")
        
    except Exception as e:
        print(f"❌ 测试过程中发生错误: {e}")
        import traceback
        traceback.print_exc()

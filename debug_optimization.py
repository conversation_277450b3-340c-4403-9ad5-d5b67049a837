#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
调试优化问题
"""

import numpy as np

def debug_data_split():
    """调试数据分割问题"""
    print("🔍 调试数据分割...")
    
    # 模拟历史数据
    total_periods = 1000
    historical_data = np.random.randint(0, 10, (total_periods, 5))
    
    print(f"总数据: {len(historical_data)}期")
    
    # 模拟数据分割
    train_size = int(len(historical_data) * 0.7)
    validation_size = int(len(historical_data) * 0.15)
    test_size = len(historical_data) - train_size - validation_size
    
    train_data = historical_data[:train_size]
    validation_data = historical_data[train_size:train_size + validation_size]
    test_data = historical_data[train_size + validation_size:]
    
    print(f"训练集: {len(train_data)}期 ({len(train_data)/len(historical_data)*100:.1f}%)")
    print(f"验证集: {len(validation_data)}期 ({len(validation_data)/len(historical_data)*100:.1f}%)")
    print(f"测试集: {len(test_data)}期 ({len(test_data)/len(historical_data)*100:.1f}%)")
    
    # 检查回测期数计算
    validation_backtest = min(max(20, len(validation_data)//3), len(validation_data)-10)
    train_backtest = min(max(30, len(train_data)//5), len(train_data)-20)
    
    print(f"\n回测期数:")
    print(f"验证集回测: {validation_backtest}期")
    print(f"训练集回测: {train_backtest}期")
    
    if validation_backtest <= 0:
        print("❌ 验证集回测期数为0，这会导致评估失败")
    else:
        print("✅ 验证集回测期数正常")
    
    if train_backtest <= 0:
        print("❌ 训练集回测期数为0，这会导致评估失败")
    else:
        print("✅ 训练集回测期数正常")

def debug_evaluation_function():
    """调试评估函数"""
    print("\n🔍 调试评估函数...")
    
    # 模拟参数
    test_params = {
        'alpha': 2.5,
        'lambda': 0.15,
        'short_weight': 0.25,
        'mid_weight': 0.35,
        'long_weight': 0.30,
        'co_weight': 0.10,
        'hot_threshold': 2.0,
        'cold_threshold': 8.0,
        'hot_multiplier': 1.5,
        'cold_multiplier': 1.2,
        'window': 50,
        'periodicity': 12,
        'selection_count': 2
    }
    
    print(f"测试参数: {test_params}")
    
    # 模拟数据
    validation_data = np.random.randint(0, 10, (150, 5))  # 150期验证数据
    backtest_periods = 20
    
    print(f"验证数据: {len(validation_data)}期")
    print(f"回测期数: {backtest_periods}期")
    
    # 模拟评估过程
    total_hits = 0
    valid_tests = 0
    
    for i in range(len(validation_data) - backtest_periods, len(validation_data)):
        train_data = validation_data[:i]
        
        if len(train_data) < 20:
            continue
            
        valid_tests += 1
        
        # 模拟预测和命中检查
        # 简化：随机生成命中结果
        if np.random.random() < 0.25:  # 25%的命中率
            total_hits += 1
    
    hit_rate = total_hits / valid_tests if valid_tests > 0 else 0.0
    
    print(f"有效测试: {valid_tests}次")
    print(f"命中次数: {total_hits}次")
    print(f"命中率: {hit_rate:.3f} ({hit_rate*100:.1f}%)")
    
    if hit_rate == 0.0:
        print("❌ 命中率为0，可能存在问题")
    else:
        print("✅ 评估结果正常")

def debug_genetic_algorithm():
    """调试遗传算法"""
    print("\n🔍 调试遗传算法...")
    
    # 模拟评估函数
    def mock_evaluate_function(params):
        # 简单的评估函数：基于参数的某种组合
        score = (params.get('alpha', 1.0) * 0.1 + 
                params.get('lambda', 0.1) * 0.5 +
                params.get('short_weight', 0.2) * 0.3)
        # 添加一些随机性
        score += np.random.normal(0, 0.05)
        return max(0, min(1, score))  # 限制在0-1之间
    
    # 模拟遗传算法的一次迭代
    population_size = 10
    population = []
    
    # 生成初始种群
    for _ in range(population_size):
        params = {
            'alpha': np.random.uniform(1.0, 5.0),
            'lambda': np.random.uniform(0.05, 0.3),
            'short_weight': np.random.uniform(0.1, 0.5),
            'mid_weight': np.random.uniform(0.1, 0.5),
            'long_weight': np.random.uniform(0.1, 0.5),
            'co_weight': np.random.uniform(0.05, 0.2)
        }
        population.append(params)
    
    print(f"初始种群大小: {len(population)}")
    
    # 评估种群
    evaluated_population = []
    for params in population:
        score = mock_evaluate_function(params)
        evaluated_population.append((params, score))
        print(f"参数评估: {score:.3f}")
    
    # 排序
    evaluated_population.sort(key=lambda x: x[1], reverse=True)
    best_score = evaluated_population[0][1]
    best_params = evaluated_population[0][0]
    
    print(f"最佳分数: {best_score:.3f}")
    print(f"最佳参数: {best_params}")
    
    if best_score > 0:
        print("✅ 遗传算法评估正常")
    else:
        print("❌ 遗传算法评估异常")

def debug_progress_callback():
    """调试进度回调"""
    print("\n🔍 调试进度回调...")
    
    # 模拟进度回调
    def mock_progress_callback(iteration, total, current_best_rate, current_params):
        print(f"迭代 {iteration}/{total}: 最佳分数 {current_best_rate:.3f}")
        return True  # 继续优化
    
    # 模拟优化过程
    max_iterations = 5
    for i in range(max_iterations):
        current_best_rate = 0.2 + i * 0.05  # 模拟逐渐提高的分数
        current_params = {'alpha': 2.0 + i * 0.1}
        
        result = mock_progress_callback(i + 1, max_iterations, current_best_rate, current_params)
        if not result:
            print("优化被中断")
            break
    
    print("✅ 进度回调测试完成")

if __name__ == "__main__":
    print("🔧 优化问题调试工具\n")
    
    try:
        debug_data_split()
        debug_evaluation_function()
        debug_genetic_algorithm()
        debug_progress_callback()
        
        print("\n📋 调试总结:")
        print("1. 检查数据分割是否正确")
        print("2. 检查评估函数是否返回有效结果")
        print("3. 检查遗传算法是否正常工作")
        print("4. 检查进度回调是否正常")
        print("\n如果所有测试都通过，问题可能在于:")
        print("- 验证集数据质量")
        print("- 模型参数范围设置")
        print("- 评估函数的具体实现")
        
    except Exception as e:
        print(f"❌ 调试过程中发生错误: {e}")
        import traceback
        traceback.print_exc()

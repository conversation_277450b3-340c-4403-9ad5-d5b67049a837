#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
启动调试测试脚本
用于诊断主程序启动问题
"""

import sys
import traceback

def test_imports():
    """测试所有必要的导入"""
    print("测试导入...")
    
    try:
        import pandas as pd
        print("✅ pandas 导入成功")
    except Exception as e:
        print(f"❌ pandas 导入失败: {e}")
        return False
    
    try:
        import numpy as np
        print("✅ numpy 导入成功")
    except Exception as e:
        print(f"❌ numpy 导入失败: {e}")
        return False
    
    try:
        import tkinter as tk
        from tkinter import filedialog, messagebox, ttk, scrolledtext
        print("✅ tkinter 导入成功")
    except Exception as e:
        print(f"❌ tkinter 导入失败: {e}")
        return False
    
    try:
        import matplotlib.pyplot as plt
        from matplotlib.backends.backend_tkagg import FigureCanvasTkAgg
        import matplotlib
        matplotlib.use("TkAgg")
        print("✅ matplotlib 导入成功")
    except Exception as e:
        print(f"❌ matplotlib 导入失败: {e}")
        return False
    
    try:
        import threading
        import random
        import json
        import os
        import time
        from datetime import datetime
        print("✅ 其他模块导入成功")
    except Exception as e:
        print(f"❌ 其他模块导入失败: {e}")
        return False
    
    return True

def test_config_loading():
    """测试配置文件加载"""
    print("\n测试配置文件加载...")
    
    try:
        import json
        import os
        
        config_file = "config.json"
        if os.path.exists(config_file):
            with open(config_file, 'r', encoding='utf-8') as f:
                config = json.load(f)
            print("✅ 配置文件加载成功")
            print(f"配置项数量: {len(config)}")
            return True
        else:
            print("⚠️ 配置文件不存在")
            return True  # 这不是错误
    except Exception as e:
        print(f"❌ 配置文件加载失败: {e}")
        return False

def test_class_creation():
    """测试类的基本创建"""
    print("\n测试类创建...")
    
    try:
        # 尝试导入主程序中的类
        sys.path.insert(0, '.')
        
        # 先测试基础类
        from importlib import import_module
        
        # 动态导入主模块
        spec = import_module('67(3)')
        print("✅ 主模块导入成功")
        
        # 测试创建基础类
        MFTNModel = getattr(spec, 'MFTNModel')
        model = MFTNModel()
        print("✅ MFTNModel 创建成功")
        
        OnlineLearningSystem = getattr(spec, 'OnlineLearningSystem')
        params = {'alpha': 2.0, 'lambda': 0.1}
        online_system = OnlineLearningSystem(params)
        print("✅ OnlineLearningSystem 创建成功")
        
        return True
        
    except Exception as e:
        print(f"❌ 类创建失败: {e}")
        traceback.print_exc()
        return False

def test_tkinter_basic():
    """测试基础Tkinter功能"""
    print("\n测试基础Tkinter功能...")
    
    try:
        import tkinter as tk
        
        # 创建基础窗口
        root = tk.Tk()
        root.title("测试窗口")
        root.geometry("300x200")
        
        # 添加标签
        label = tk.Label(root, text="测试标签")
        label.pack()
        
        print("✅ 基础Tkinter窗口创建成功")
        
        # 立即销毁窗口
        root.destroy()
        print("✅ 窗口销毁成功")
        
        return True
        
    except Exception as e:
        print(f"❌ Tkinter测试失败: {e}")
        traceback.print_exc()
        return False

def test_main_app_creation():
    """测试主应用程序创建"""
    print("\n测试主应用程序创建...")
    
    try:
        import tkinter as tk
        sys.path.insert(0, '.')
        
        # 导入主程序
        from importlib import import_module
        spec = import_module('67(3)')
        
        # 创建根窗口
        root = tk.Tk()
        root.withdraw()  # 隐藏窗口，避免显示
        
        print("正在创建LotteryPredictionApp...")
        
        # 创建应用程序实例
        LotteryPredictionApp = getattr(spec, 'LotteryPredictionApp')
        app = LotteryPredictionApp(root)
        
        print("✅ LotteryPredictionApp 创建成功")
        
        # 立即销毁
        root.destroy()
        print("✅ 应用程序销毁成功")
        
        return True
        
    except Exception as e:
        print(f"❌ 主应用程序创建失败: {e}")
        traceback.print_exc()
        return False

def main():
    """主测试函数"""
    print("=" * 60)
    print("启动调试测试")
    print("=" * 60)
    
    tests = [
        ("导入测试", test_imports),
        ("配置文件测试", test_config_loading),
        ("基础Tkinter测试", test_tkinter_basic),
        ("类创建测试", test_class_creation),
        ("主应用程序测试", test_main_app_creation),
    ]
    
    results = []
    
    for test_name, test_func in tests:
        print(f"\n{'='*20} {test_name} {'='*20}")
        try:
            result = test_func()
            results.append((test_name, result))
            if result:
                print(f"✅ {test_name} 通过")
            else:
                print(f"❌ {test_name} 失败")
        except Exception as e:
            print(f"❌ {test_name} 异常: {e}")
            traceback.print_exc()
            results.append((test_name, False))
    
    print("\n" + "=" * 60)
    print("测试结果总结")
    print("=" * 60)
    
    for test_name, result in results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"{test_name}: {status}")
    
    all_passed = all(result for _, result in results)
    
    if all_passed:
        print("\n🎉 所有测试通过！主程序应该可以正常启动。")
    else:
        print("\n⚠️ 存在问题，需要进一步调试。")
    
    return all_passed

if __name__ == "__main__":
    main()

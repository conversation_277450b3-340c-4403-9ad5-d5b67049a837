#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试按钮功能的调试脚本
"""

import tkinter as tk
from tkinter import messagebox

def test_button_functionality():
    """测试按钮基本功能"""
    print("创建测试窗口...")
    
    root = tk.Tk()
    root.title("按钮功能测试")
    root.geometry("400x200")
    
    # 测试变量
    button_clicked = False
    
    def test_callback():
        nonlocal button_clicked
        button_clicked = True
        print("按钮被点击了！")
        messagebox.showinfo("成功", "按钮功能正常")
        result_label.config(text="✅ 按钮点击成功", fg="green")
    
    def test_disabled_callback():
        print("禁用按钮被点击（不应该看到这个消息）")
        messagebox.showwarning("警告", "禁用按钮不应该响应")
    
    # 创建按钮
    normal_button = tk.Button(root, text="正常按钮", command=test_callback,
                             font=("Sim<PERSON>ei", 12), bg="#4CAF50", fg="white",
                             relief=tk.RAISED, padx=20)
    normal_button.pack(pady=10)
    
    disabled_button = tk.Button(root, text="禁用按钮", command=test_disabled_callback,
                               font=("SimHei", 12), bg="#f44336", fg="white",
                               relief=tk.RAISED, padx=20, state='disabled')
    disabled_button.pack(pady=10)
    
    # 启用禁用按钮的按钮
    def enable_disabled_button():
        disabled_button.config(state='normal')
        enable_button.config(text="已启用", state='disabled')
        result_label.config(text="禁用按钮已启用，请测试", fg="blue")
    
    enable_button = tk.Button(root, text="启用禁用按钮", command=enable_disabled_button,
                             font=("SimHei", 10), bg="#FF9800", fg="white")
    enable_button.pack(pady=5)
    
    # 结果显示
    result_label = tk.Label(root, text="请点击按钮测试功能", font=("SimHei", 10))
    result_label.pack(pady=10)
    
    # 退出按钮
    def close_window():
        print(f"测试结果: 按钮点击状态 = {button_clicked}")
        root.destroy()
    
    close_button = tk.Button(root, text="关闭", command=close_window,
                            font=("SimHei", 10))
    close_button.pack(pady=5)
    
    print("测试窗口已创建，请测试按钮功能")
    root.mainloop()
    
    return button_clicked

def simulate_apply_params_issue():
    """模拟应用参数按钮的问题"""
    print("\n模拟应用参数按钮问题...")
    
    root = tk.Tk()
    root.title("应用参数按钮测试")
    root.geometry("500x300")
    
    # 模拟日志显示
    log_text = tk.Text(root, height=10, width=60)
    log_text.pack(pady=10)
    
    def add_log(message):
        log_text.insert(tk.END, message + "\n")
        log_text.see(tk.END)
        print(f"LOG: {message}")
    
    # 模拟优化结果
    mock_results = {
        'best_params': {
            'alpha': 2.5,
            'lambda': 0.15,
            'short_weight': 0.25,
            'mid_weight': 0.35,
            'long_weight': 0.30,
            'co_weight': 0.10
        },
        'test_score': 0.267
    }
    
    def mock_apply_best_params():
        add_log("=== 应用最佳参数按钮被点击 ===")
        
        # 检查是否有优化结果
        if hasattr(mock_apply_best_params, 'optimization_results'):
            best_params = mock_apply_best_params.optimization_results.get('best_params')
            add_log(f"找到最佳参数: {len(best_params)}个")
            
            # 显示参数
            for k, v in best_params.items():
                add_log(f"  {k}: {v:.4f}")
            
            add_log("✅ 参数应用成功")
            messagebox.showinfo("成功", "参数应用成功！")
            
            # 禁用按钮
            apply_button.config(state='disabled', text="已应用参数")
        else:
            add_log("❌ 没有找到优化结果")
            messagebox.showwarning("警告", "没有找到可应用的参数")
    
    def simulate_optimization_complete():
        add_log("模拟优化完成...")
        # 存储优化结果
        mock_apply_best_params.optimization_results = mock_results
        add_log("优化结果已存储")
        
        # 启用按钮
        apply_button.config(state='normal')
        add_log("应用按钮已启用")
    
    # 创建按钮
    simulate_button = tk.Button(root, text="模拟优化完成", command=simulate_optimization_complete,
                               font=("SimHei", 10), bg="#4CAF50", fg="white")
    simulate_button.pack(pady=5)
    
    apply_button = tk.Button(root, text="应用最佳参数", command=mock_apply_best_params,
                            font=("SimHei", 10), bg="#2196F3", fg="white",
                            state='disabled')
    apply_button.pack(pady=5)
    
    close_button = tk.Button(root, text="关闭", command=root.destroy,
                            font=("SimHei", 10))
    close_button.pack(pady=5)
    
    add_log("测试环境已准备就绪")
    add_log("1. 点击'模拟优化完成'按钮")
    add_log("2. 然后点击'应用最佳参数'按钮")
    
    root.mainloop()

if __name__ == "__main__":
    print("🔧 按钮功能调试测试\n")
    
    try:
        print("1. 测试基本按钮功能...")
        basic_test = test_button_functionality()
        
        if basic_test:
            print("✅ 基本按钮功能正常")
        else:
            print("❌ 基本按钮功能异常")
        
        print("\n2. 测试应用参数按钮模拟...")
        simulate_apply_params_issue()
        
    except Exception as e:
        print(f"❌ 测试过程中发生错误: {e}")
        import traceback
        traceback.print_exc()

#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
调试50%进度检测问题
"""

def test_50_percent_detection():
    """测试50%进度检测逻辑"""
    print("🔍 测试50%进度检测逻辑")
    print("=" * 50)
    
    # 测试不同的迭代次数设置
    test_cases = [
        {"max_iter": 100, "name": "100次迭代"},
        {"max_iter": 200, "name": "200次迭代"},
        {"max_iter": 50, "name": "50次迭代"},
        {"max_iter": 20, "name": "20次迭代"}
    ]
    
    for case in test_cases:
        max_iterations = case["max_iter"]
        print(f"\n📊 {case['name']}:")
        
        trigger_iterations = []
        
        for iteration in range(1, max_iterations + 1):
            progress_percent = (iteration / max_iterations) * 100
            
            # 检查是否在50%范围内
            if 45 <= progress_percent <= 55:
                trigger_iterations.append((iteration, progress_percent))
        
        if trigger_iterations:
            print(f"  触发范围: 第{trigger_iterations[0][0]}-{trigger_iterations[-1][0]}次迭代")
            print(f"  进度范围: {trigger_iterations[0][1]:.1f}%-{trigger_iterations[-1][1]:.1f}%")
            print(f"  首次触发: 第{trigger_iterations[0][0]}次 ({trigger_iterations[0][1]:.1f}%)")
        else:
            print("  ❌ 无法触发50%检测")

def simulate_progress_callback():
    """模拟进度回调过程"""
    print("\n🎭 模拟进度回调过程")
    print("=" * 50)
    
    # 模拟进度回调函数
    class MockProgressCallback:
        def __init__(self):
            self.total_iterations = 100
            self.test_data = list(range(150))  # 模拟150期测试数据
            self.test_evaluated_at_50 = False
    
    progress_callback = MockProgressCallback()
    
    print(f"总迭代次数: {progress_callback.total_iterations}")
    print(f"测试数据: {len(progress_callback.test_data)}期")
    print(f"50%评估标记: {progress_callback.test_evaluated_at_50}")
    
    # 模拟优化过程
    for iteration in range(1, progress_callback.total_iterations + 1):
        progress_percent = (iteration / progress_callback.total_iterations) * 100
        
        # 检查50%进度
        if 45 <= progress_percent <= 55:
            print(f"\n迭代{iteration}: 进度{progress_percent:.1f}%在50%范围内")
            
            if not hasattr(progress_callback, 'test_evaluated_at_50') or not progress_callback.test_evaluated_at_50:
                print("  ✅ 满足50%评估条件")
                
                # 检查数据可用性
                has_test_data = hasattr(progress_callback, 'test_data') and progress_callback.test_data is not None
                print(f"  测试数据可用: {has_test_data}")
                
                if has_test_data:
                    print(f"  测试数据大小: {len(progress_callback.test_data)}期")
                    print("  🎯 应该触发50%进度评估")
                    
                    # 模拟评估
                    test_score = 0.267  # 模拟测试集命中率
                    print(f"  模拟测试集命中率: {test_score*100:.1f}%")
                    
                    # 设置标记
                    progress_callback.test_evaluated_at_50 = True
                    print("  标记已设置，后续不会重复评估")
                    break
            else:
                print("  ⏭️ 50%评估已完成，跳过")

def check_common_issues():
    """检查常见问题"""
    print("\n🔍 检查常见问题")
    print("=" * 50)
    
    print("可能导致50%进度不显示的原因:")
    
    print("\n1. 进度计算问题:")
    print("   - total_iterations 未正确设置")
    print("   - 进度百分比计算错误")
    print("   - 45%-55%范围太窄，错过触发时机")
    
    print("\n2. 条件检查失败:")
    print("   - test_data 为 None 或未设置")
    print("   - current_params 为 None")
    print("   - test_evaluated_at_50 标记已设置")
    
    print("\n3. 异常被捕获:")
    print("   - _evaluate_params_on_data 方法出错")
    print("   - UI更新失败")
    print("   - 数据格式问题")
    
    print("\n4. 时机问题:")
    print("   - 优化速度太快，错过50%检测")
    print("   - 迭代次数太少，没有合适的50%点")
    
    print("\n🛠️ 调试建议:")
    print("1. 查看控制台调试输出:")
    print("   - 'DEBUG: 迭代X/Y, 进度Z%'")
    print("   - 'DEBUG: 进度Z%在50%范围内'")
    print("   - 'DEBUG: 开始50%进度测试集评估'")
    
    print("\n2. 检查日志信息:")
    print("   - '🎯 优化进度50%，评估测试集命中率...'")
    print("   - '50%进度测试集命中率: X%'")
    
    print("\n3. 验证设置:")
    print("   - 确保迭代次数足够大(建议>50)")
    print("   - 确保有足够的测试数据")

def provide_troubleshooting_steps():
    """提供故障排除步骤"""
    print("\n🔧 故障排除步骤")
    print("=" * 50)
    
    print("如果50%进度仍不显示测试集命中率，请按以下步骤检查:")
    
    print("\n步骤1: 检查控制台输出")
    print("  查找以下调试信息:")
    print("  - 'DEBUG: 迭代X/Y, 进度Z%'")
    print("  - 'DEBUG: 进度Z%在50%范围内'")
    print("  - 'DEBUG: 测试数据可用: True/False'")
    print("  - 'DEBUG: 当前参数可用: True/False'")
    
    print("\n步骤2: 检查优化设置")
    print("  - 最大迭代次数是否足够大(建议≥100)")
    print("  - 数据量是否充足(建议≥500期)")
    
    print("\n步骤3: 检查日志信息")
    print("  查找以下日志:")
    print("  - '🎯 优化进度50%，评估测试集命中率...'")
    print("  - '50%进度测试集命中率: X%'")
    print("  - 任何错误信息")
    
    print("\n步骤4: 手动验证")
    print("  - 计算当前迭代次数是否达到总数的45-55%")
    print("  - 检查测试集数据是否存在")
    print("  - 验证UI是否正常响应")
    
    print("\n如果以上都正常但仍不显示，可能的解决方案:")
    print("  1. 重启程序重新优化")
    print("  2. 减少迭代次数到100-200次")
    print("  3. 检查数据文件是否完整")
    print("  4. 尝试使用更多的历史数据")

if __name__ == "__main__":
    print("🔧 50%进度检测调试工具")
    print("=" * 60)
    
    try:
        test_50_percent_detection()
        simulate_progress_callback()
        check_common_issues()
        provide_troubleshooting_steps()
        
        print("\n🎯 总结:")
        print("已添加详细的调试信息到进度回调函数中。")
        print("重新运行优化时，请观察控制台输出的调试信息。")
        print("这将帮助确定50%进度检测为什么没有触发。")
        
    except Exception as e:
        print(f"❌ 调试过程中发生错误: {e}")
        import traceback
        traceback.print_exc()

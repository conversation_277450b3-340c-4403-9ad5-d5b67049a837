#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试停止优化后按钮状态
"""

import numpy as np
import threading
import time

def test_optimizer_state_management():
    """测试优化器状态管理"""
    print("测试优化器状态管理...")
    
    try:
        # 导入主程序
        with open('67(3).py', 'r', encoding='utf-8') as f:
            exec(f.read())
        
        # 创建测试数据
        np.random.seed(12345)
        test_data = np.random.randint(0, 10, (200, 5))
        print(f"创建测试数据: {test_data.shape}")
        
        # 创建优化器
        optimizer = ParameterOptimizer(
            data_file="test_data.xlsx",
            target_hit_rate=0.8,
            max_iterations=50,  # 较少的迭代次数便于测试
            population_size=10,
            num_threads=1
        )
        
        print("优化器创建完成")
        
        # 测试1: 检查初始状态
        print("\n1. 检查初始状态...")
        print(f"   初始best_params: {hasattr(optimizer, 'best_params') and optimizer.best_params is not None}")
        print(f"   初始best_hit_rate: {getattr(optimizer, 'best_hit_rate', 'None')}")
        
        # 测试2: 运行短时间优化然后停止
        print("\n2. 运行短时间优化...")
        
        optimization_stopped = False
        
        def run_optimization():
            nonlocal optimization_stopped
            try:
                # 运行优化
                results = optimizer.optimize_with_strict_validation(test_data)
                if not optimization_stopped:
                    print("   优化正常完成")
                    print(f"   完成后best_params存在: {results['best_params'] is not None}")
                else:
                    print("   优化被中断")
            except Exception as e:
                print(f"   优化过程出错: {e}")
        
        # 启动优化线程
        opt_thread = threading.Thread(target=run_optimization)
        opt_thread.daemon = True
        opt_thread.start()
        
        # 等待一段时间后停止
        time.sleep(2)  # 让优化运行2秒
        optimization_stopped = True
        optimizer.optimization_running = False
        
        print("   发送停止信号...")
        
        # 等待线程结束
        opt_thread.join(timeout=5)
        
        # 测试3: 检查停止后的状态
        print("\n3. 检查停止后状态...")
        print(f"   停止后best_params存在: {hasattr(optimizer, 'best_params') and optimizer.best_params is not None}")
        print(f"   停止后best_hit_rate: {getattr(optimizer, 'best_hit_rate', 'None')}")
        
        if hasattr(optimizer, 'best_params') and optimizer.best_params:
            print("   ✅ 优化器保存了最佳参数")
            print(f"   参数数量: {len(optimizer.best_params)}")
            print(f"   命中率: {getattr(optimizer, 'best_hit_rate', 0):.3f}")
        else:
            print("   ❌ 优化器未保存最佳参数")
        
        # 测试4: 模拟停止优化的检查逻辑
        print("\n4. 模拟停止优化检查逻辑...")
        
        # 模拟apply_best_params函数
        class MockApplyBestParams:
            pass
        
        apply_best_params = MockApplyBestParams()
        current_optimizer = optimizer
        
        # 执行检查逻辑
        has_best_params = False
        best_params_source = ""
        
        # 1. 首先检查严格验证的完整结果
        if hasattr(apply_best_params, 'optimization_results') and getattr(apply_best_params, 'optimization_results', None):
            has_best_params = True
            best_params_source = "严格验证完整结果"
        # 2. 检查优化器中的最佳参数（可能是部分结果）
        elif current_optimizer and hasattr(current_optimizer, 'best_params') and current_optimizer.best_params:
            has_best_params = True
            best_params_source = "优化器部分结果"
            # 将优化器的结果转换为标准格式
            apply_best_params.optimization_results = {
                'best_params': current_optimizer.best_params,
                'test_score': getattr(current_optimizer, 'best_hit_rate', 0.0),
                'source': 'partial_optimization'
            }
        
        print(f"   检查结果: 有参数={has_best_params}, 来源={best_params_source}")
        
        if has_best_params:
            print("   ✅ 应用按钮应该被启用")
            print(f"   参数来源: {best_params_source}")
            if hasattr(apply_best_params, 'optimization_results'):
                results = apply_best_params.optimization_results
                print(f"   可应用参数数量: {len(results['best_params'])}")
                print(f"   测试分数: {results.get('test_score', 'N/A')}")
        else:
            print("   ❌ 应用按钮将保持禁用状态")
        
        return has_best_params
        
    except Exception as e:
        print(f"测试过程中发生错误: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_button_enable_logic():
    """测试按钮启用逻辑"""
    print("\n" + "="*50)
    print("测试按钮启用逻辑")
    print("="*50)
    
    # 模拟不同的状态
    test_cases = [
        {
            'name': '完整优化结果',
            'has_optimization_results': True,
            'has_optimizer_params': True,
            'expected': True
        },
        {
            'name': '仅优化器参数',
            'has_optimization_results': False,
            'has_optimizer_params': True,
            'expected': True
        },
        {
            'name': '无任何参数',
            'has_optimization_results': False,
            'has_optimizer_params': False,
            'expected': False
        }
    ]
    
    for case in test_cases:
        print(f"\n测试案例: {case['name']}")
        
        # 模拟环境
        class MockApplyBestParams:
            pass
        
        class MockOptimizer:
            def __init__(self, has_params):
                if has_params:
                    self.best_params = {'alpha': 2.0, 'lambda': 0.1}
                    self.best_hit_rate = 0.25
                else:
                    self.best_params = None
                    self.best_hit_rate = 0.0
        
        apply_best_params = MockApplyBestParams()
        
        if case['has_optimization_results']:
            apply_best_params.optimization_results = {
                'best_params': {'alpha': 3.0, 'lambda': 0.15},
                'test_score': 0.30
            }
        
        current_optimizer = MockOptimizer(case['has_optimizer_params'])
        
        # 执行检查逻辑
        has_best_params = False
        best_params_source = ""
        
        if hasattr(apply_best_params, 'optimization_results') and getattr(apply_best_params, 'optimization_results', None):
            has_best_params = True
            best_params_source = "严格验证完整结果"
        elif current_optimizer and hasattr(current_optimizer, 'best_params') and current_optimizer.best_params:
            has_best_params = True
            best_params_source = "优化器部分结果"
        
        result = "✅ 通过" if has_best_params == case['expected'] else "❌ 失败"
        print(f"   预期: {case['expected']}, 实际: {has_best_params} - {result}")
        print(f"   参数来源: {best_params_source}")

if __name__ == "__main__":
    print("🔧 测试停止优化后按钮状态\n")
    
    try:
        # 测试优化器状态管理
        optimizer_test = test_optimizer_state_management()
        
        # 测试按钮启用逻辑
        test_button_enable_logic()
        
        print("\n" + "="*50)
        print("🎯 测试结果总结:")
        print("="*50)
        
        if optimizer_test:
            print("🎉 测试通过！")
            print("✅ 优化器正确保存最佳参数")
            print("✅ 停止优化后按钮应该被启用")
            print("✅ 参数检查逻辑工作正常")
        else:
            print("❌ 测试失败")
            print("需要进一步检查优化器状态管理")
        
        print("\n📋 修复说明:")
        print("1. 优化过程中实时保存最佳参数到优化器属性")
        print("2. 停止优化时检查优化器中的部分结果")
        print("3. 将部分结果转换为标准格式供应用按钮使用")
        print("4. 添加详细的调试信息跟踪状态变化")
        
    except Exception as e:
        print(f"❌ 测试过程中发生错误: {e}")
        import traceback
        traceback.print_exc()

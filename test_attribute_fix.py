#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试属性错误修复
"""

def test_attribute_access():
    """测试属性访问问题"""
    print("🔍 测试属性访问修复")
    print("=" * 40)
    
    # 模拟优化器对象
    class MockOptimizer:
        def __init__(self):
            self.max_iterations = 200
            self.best_params = {'alpha': 2.0, 'lambda': 0.1}
            self.best_hit_rate = 0.25
    
    # 模拟应用对象
    class MockApp:
        def __init__(self):
            self.history_data = list(range(1000))  # 模拟1000期数据
            # 注意：没有max_iterations属性
    
    # 模拟进度回调函数
    class MockProgressCallback:
        pass
    
    print("1. 测试修复前的问题:")
    app = MockApp()
    optimizer = MockOptimizer()
    progress_callback = MockProgressCallback()
    
    # 模拟修复前的错误代码
    try:
        # progress_callback.total_iterations = app.max_iterations  # 这会出错
        print("   ❌ app.max_iterations 不存在，会导致 AttributeError")
    except AttributeError as e:
        print(f"   错误: {e}")
    
    print("\n2. 测试修复后的代码:")
    try:
        # 修复后的代码
        progress_callback.total_iterations = optimizer.max_iterations  # 从优化器获取
        print(f"   ✅ 成功获取 total_iterations: {progress_callback.total_iterations}")
    except AttributeError as e:
        print(f"   错误: {e}")
    
    print("\n3. 测试数据获取:")
    
    # 测试从进度回调获取数据
    progress_callback.test_data = [1, 2, 3, 4, 5] * 20  # 模拟100期测试数据
    
    if hasattr(progress_callback, 'test_data') and progress_callback.test_data is not None:
        print(f"   ✅ 从进度回调获取测试数据: {len(progress_callback.test_data)}期")
    else:
        print("   ❌ 无法从进度回调获取测试数据")
        
        # 备用方案
        if hasattr(app, 'history_data') and app.history_data is not None:
            total_periods = len(app.history_data)
            train_size = int(total_periods * 0.7)
            validation_size = int(total_periods * 0.15)
            test_data = app.history_data[train_size + validation_size:]
            print(f"   ✅ 备用方案获取测试数据: {len(test_data)}期")
        else:
            print("   ❌ 备用方案也失败")

def test_progress_calculation():
    """测试进度计算"""
    print("\n🔍 测试进度计算")
    print("=" * 40)
    
    test_cases = [
        {"current": 50, "total": 100, "expected_trigger": True},
        {"current": 100, "total": 200, "expected_trigger": True},
        {"current": 25, "total": 50, "expected_trigger": True},
        {"current": 10, "total": 100, "expected_trigger": False},
        {"current": 90, "total": 100, "expected_trigger": False},
    ]
    
    for case in test_cases:
        current = case["current"]
        total = case["total"]
        expected = case["expected_trigger"]
        
        progress_percent = (current / total) * 100
        will_trigger = 45 <= progress_percent <= 55
        
        result = "✅" if will_trigger == expected else "❌"
        print(f"   迭代{current}/{total} ({progress_percent:.1f}%) - 触发50%评估: {will_trigger} {result}")

def test_safe_attribute_access():
    """测试安全的属性访问"""
    print("\n🔍 测试安全的属性访问")
    print("=" * 40)
    
    class TestObject:
        def __init__(self):
            self.existing_attr = "存在的属性"
    
    obj = TestObject()
    
    # 测试安全的属性检查
    print("1. 检查存在的属性:")
    if hasattr(obj, 'existing_attr'):
        print(f"   ✅ existing_attr: {obj.existing_attr}")
    else:
        print("   ❌ existing_attr 不存在")
    
    print("\n2. 检查不存在的属性:")
    if hasattr(obj, 'non_existing_attr'):
        print(f"   ✅ non_existing_attr: {obj.non_existing_attr}")
    else:
        print("   ✅ non_existing_attr 不存在，安全跳过")
    
    print("\n3. 使用 getattr 的安全访问:")
    value1 = getattr(obj, 'existing_attr', '默认值')
    value2 = getattr(obj, 'non_existing_attr', '默认值')
    print(f"   existing_attr: {value1}")
    print(f"   non_existing_attr: {value2}")

def demonstrate_fix():
    """演示修复方案"""
    print("\n🛠️ 演示修复方案")
    print("=" * 40)
    
    print("修复前的问题代码:")
    print("```python")
    print("progress_callback.total_iterations = self.max_iterations  # ❌ 错误")
    print("```")
    print("错误: LotteryPredictionApp 对象没有 max_iterations 属性")
    
    print("\n修复后的正确代码:")
    print("```python")
    print("progress_callback.total_iterations = current_optimizer.max_iterations  # ✅ 正确")
    print("```")
    print("说明: 从优化器对象获取 max_iterations 属性")
    
    print("\n类似的修复:")
    print("1. 数据获取优先级:")
    print("   - 首选: 从 progress_callback.test_data 获取")
    print("   - 备用: 从 self.history_data 重新计算")
    print("   - 安全: 检查数据是否为 None")
    
    print("\n2. 安全的属性访问:")
    print("   - 使用 hasattr() 检查属性是否存在")
    print("   - 使用 getattr() 提供默认值")
    print("   - 添加 None 检查")

if __name__ == "__main__":
    print("🔧 属性错误修复测试")
    print("=" * 50)
    
    try:
        test_attribute_access()
        test_progress_calculation()
        test_safe_attribute_access()
        demonstrate_fix()
        
        print("\n🎯 修复总结:")
        print("✅ 修复了 max_iterations 属性访问错误")
        print("✅ 改为从优化器对象获取属性")
        print("✅ 添加了安全的数据获取机制")
        print("✅ 增强了错误处理和备用方案")
        
        print("\n现在程序应该可以正常运行，不会出现 AttributeError 了。")
        
    except Exception as e:
        print(f"❌ 测试过程中发生错误: {e}")
        import traceback
        traceback.print_exc()

#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试按钮样式和交互
"""

import tkinter as tk
from tkinter import messagebox

def test_button_styles():
    """测试不同状态下的按钮样式"""
    root = tk.Tk()
    root.title("按钮样式测试")
    root.geometry("600x400")
    
    # 创建测试框架
    frame = tk.Frame(root)
    frame.pack(pady=20)
    
    # 状态标签
    status_label = tk.Label(root, text="按钮状态测试", font=("SimHei", 14))
    status_label.pack(pady=10)
    
    def test_callback():
        messagebox.showinfo("测试", "按钮点击成功！")
        status_label.config(text="✅ 按钮点击成功", fg="green")
    
    # 1. 正常启用按钮
    normal_button = tk.Button(frame, text="正常启用按钮", command=test_callback,
                             font=("SimHei", 10), bg="#2196F3", fg="white",
                             relief=tk.RAISED, padx=20, cursor="hand2")
    normal_button.pack(side=tk.LEFT, padx=5)
    
    # 2. 禁用按钮（旧样式）
    disabled_old = tk.Button(frame, text="禁用按钮(旧)", command=test_callback,
                            font=("SimHei", 10), bg="#2196F3", fg="white",
                            relief=tk.RAISED, padx=20, state='disabled')
    disabled_old.pack(side=tk.LEFT, padx=5)
    
    # 3. 禁用按钮（新样式）
    disabled_new = tk.Button(frame, text="禁用按钮(新)", command=test_callback,
                            font=("SimHei", 10), bg="#CCCCCC", fg="#666666",
                            relief=tk.RAISED, padx=20, state='disabled',
                            disabledforeground="#666666", cursor="arrow")
    disabled_new.pack(side=tk.LEFT, padx=5)
    
    # 4. 应用最佳参数按钮样式测试
    apply_button = tk.Button(frame, text="应用最佳参数", command=test_callback,
                            font=("SimHei", 10), bg="#CCCCCC", fg="#666666",
                            relief=tk.RAISED, padx=20, state='disabled',
                            disabledforeground="#666666", 
                            activebackground="#2196F3", activeforeground="white",
                            cursor="hand2")
    apply_button.pack(side=tk.LEFT, padx=5)
    
    # 存储按钮样式
    apply_button.enabled_style = {
        'bg': '#2196F3', 'fg': 'white', 'state': 'normal',
        'relief': tk.RAISED, 'cursor': 'hand2'
    }
    apply_button.disabled_style = {
        'bg': '#CCCCCC', 'fg': '#666666', 'state': 'disabled',
        'relief': tk.RAISED, 'cursor': 'arrow'
    }
    
    # 控制按钮
    control_frame = tk.Frame(root)
    control_frame.pack(pady=20)
    
    def enable_apply_button():
        apply_button.config(**apply_button.enabled_style)
        status_label.config(text="应用按钮已启用", fg="blue")
        
        # 添加悬停效果
        def on_enter(event):
            _ = event
            if apply_button['state'] == 'normal':
                apply_button.config(bg='#1976D2')
        
        def on_leave(event):
            _ = event
            if apply_button['state'] == 'normal':
                apply_button.config(bg='#2196F3')
        
        apply_button.bind("<Enter>", on_enter)
        apply_button.bind("<Leave>", on_leave)
    
    def disable_apply_button():
        apply_button.config(**apply_button.disabled_style)
        status_label.config(text="应用按钮已禁用", fg="red")
    
    def mark_applied():
        apply_button.config(state='disabled', text="已应用参数", 
                          bg='#4CAF50', fg='white', cursor='arrow')
        status_label.config(text="参数已应用", fg="green")
    
    tk.Button(control_frame, text="启用应用按钮", command=enable_apply_button,
             bg="#4CAF50", fg="white", padx=10).pack(side=tk.LEFT, padx=5)
    
    tk.Button(control_frame, text="禁用应用按钮", command=disable_apply_button,
             bg="#FF9800", fg="white", padx=10).pack(side=tk.LEFT, padx=5)
    
    tk.Button(control_frame, text="标记已应用", command=mark_applied,
             bg="#9C27B0", fg="white", padx=10).pack(side=tk.LEFT, padx=5)
    
    # 说明文字
    info_text = """
测试说明：
1. 正常启用按钮 - 标准的可点击按钮
2. 禁用按钮(旧) - 原来的禁用样式，可能看起来不明显
3. 禁用按钮(新) - 改进的禁用样式，更明显的灰色
4. 应用最佳参数 - 实际使用的按钮样式

使用控制按钮测试应用按钮的不同状态：
- 启用应用按钮：变为蓝色可点击状态
- 禁用应用按钮：变为灰色不可点击状态  
- 标记已应用：变为绿色表示已完成状态
"""
    
    info_label = tk.Label(root, text=info_text, font=("SimHei", 9), 
                         justify=tk.LEFT, fg="gray")
    info_label.pack(pady=10)
    
    # 关闭按钮
    tk.Button(root, text="关闭", command=root.destroy,
             bg="#f44336", fg="white", padx=20).pack(pady=10)
    
    root.mainloop()

if __name__ == "__main__":
    print("🎨 按钮样式测试")
    print("测试不同状态下按钮的外观和交互")
    
    try:
        test_button_styles()
        print("✅ 按钮样式测试完成")
    except Exception as e:
        print(f"❌ 测试过程中发生错误: {e}")
        import traceback
        traceback.print_exc()

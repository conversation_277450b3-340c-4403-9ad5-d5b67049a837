#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
检查优化状态和测试集显示问题
"""

import time

def check_optimization_progress():
    """检查优化进度和可能的问题"""
    print("🔍 优化状态检查工具")
    print("=" * 50)
    
    print("\n📋 测试集命中率显示时机:")
    print("1. ⏳ 优化进行中 → 测试集显示: '--'")
    print("2. ✅ 优化完成 → 计算测试集命中率")
    print("3. 📊 UI更新 → 显示测试集结果")
    
    print("\n🔍 可能的问题原因:")
    print("❌ 优化还在进行中（需要等待完成）")
    print("❌ 优化过程中出现异常")
    print("❌ 优化被手动停止")
    print("❌ UI更新失败")
    print("❌ 测试集数据问题")
    
    print("\n📊 如何判断优化状态:")
    print("✅ 查看迭代进度是否显示'优化完成'")
    print("✅ 查看验证集命中率是否停止变化")
    print("✅ 查看控制台是否有'测试集评估完成'消息")
    print("✅ 查看是否有错误信息")
    
    print("\n🛠️ 排查步骤:")
    print("1. 检查控制台输出:")
    print("   - 是否显示'在测试集上评估最终性能...'")
    print("   - 是否显示'测试集性能: X.XXX'")
    print("   - 是否显示'✅ 测试集评估完成'")
    print("   - 是否有任何错误信息")
    
    print("\n2. 检查UI状态:")
    print("   - 迭代进度是否显示'优化完成'")
    print("   - 进度条是否达到100%")
    print("   - 验证集命中率是否已固定")
    
    print("\n3. 检查优化设置:")
    print("   - 最大迭代次数设置")
    print("   - 是否手动停止了优化")
    print("   - 数据量是否足够")
    
    print("\n⚠️ 常见问题和解决方案:")
    print("问题1: 优化时间过长")
    print("  解决: 减少迭代次数或种群大小")
    
    print("\n问题2: 优化过程中出错")
    print("  解决: 查看控制台错误信息")
    
    print("\n问题3: 测试集数据太小")
    print("  解决: 增加历史数据量")
    
    print("\n问题4: UI显示异常")
    print("  解决: 重启程序重新优化")

def simulate_optimization_completion():
    """模拟优化完成过程"""
    print("\n🎭 模拟优化完成过程:")
    print("=" * 30)
    
    # 模拟优化进行中
    for i in range(1, 6):
        print(f"迭代 {i}: 新的最佳分数 {0.6 + i*0.05:.3f}")
        time.sleep(0.5)
    
    print("\n遗传算法优化完成，最佳分数: 0.850")
    print("\n" + "=" * 40)
    print("在测试集上评估最终性能...")
    print("=" * 40)
    
    print("测试集数据: 150期")
    print("测试集回测期数: 30期")
    
    # 模拟测试集评估
    print("正在评估测试集...")
    time.sleep(1)
    
    test_score = 0.267
    print(f"测试集性能: {test_score:.3f} ({test_score*100:.1f}%)")
    print("✅ 测试集评估完成")
    
    print("\n📊 UI应该显示:")
    print(f"验证集命中率: 85.00%")
    print(f"测试集4位全中命中率: {test_score*100:.1f}%")  # 这里应该显示
    
    print("\n🎯 如果测试集命中率仍显示'--'，可能的原因:")
    print("1. UI更新失败")
    print("2. 程序异常中断")
    print("3. 显示变量绑定问题")

def check_typical_scores():
    """检查典型的命中率范围"""
    print("\n📊 典型命中率范围参考:")
    print("=" * 30)
    
    print("🎯 验证集命中率（优化目标）:")
    print("  正常范围: 20% - 40%")
    print("  异常高值: >60% (可能过拟合)")
    print("  当前显示: 80% (明显过高，需要关注)")
    
    print("\n🎯 测试集命中率（真实能力）:")
    print("  正常范围: 15% - 35%")
    print("  优秀表现: >30%")
    print("  随机基准: ~10% - 16%")
    
    print("\n⚠️ 如果验证集80%，测试集可能:")
    print("  乐观估计: 25% - 35%")
    print("  现实估计: 20% - 30%")
    print("  悲观估计: 15% - 25%")
    
    print("\n💡 建议:")
    print("1. 等待测试集结果确认真实能力")
    print("2. 如果测试集<20%，考虑增加数据量")
    print("3. 如果测试集>30%，说明效果不错")

if __name__ == "__main__":
    try:
        check_optimization_progress()
        
        print("\n" + "="*50)
        input("按回车键查看模拟优化完成过程...")
        
        simulate_optimization_completion()
        
        print("\n" + "="*50)
        input("按回车键查看典型命中率范围...")
        
        check_typical_scores()
        
        print("\n🎯 总结:")
        print("测试集命中率只在优化完全完成后显示")
        print("如果一直显示'--'，请检查:")
        print("1. 优化是否真的完成了")
        print("2. 控制台是否有错误信息")
        print("3. 是否需要等待更长时间")
        
    except KeyboardInterrupt:
        print("\n检查工具已退出")
    except Exception as e:
        print(f"检查过程中发生错误: {e}")

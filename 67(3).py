import pandas as pd
import numpy as np
import tkinter as tk
from tkinter import filedialog, messagebox, ttk, scrolledtext
import matplotlib.pyplot as plt
from matplotlib.backends.backend_tkagg import FigureCanvasTkAgg
import threading
import matplotlib
import random
import json
import os
import sys
import time
from datetime import datetime
matplotlib.use("TkAgg")

# 定义配置文件路径，可根据实际情况修改
CONFIG_FILE = os.path.join(os.path.dirname(__file__), 'config.json')

# 默认配置
DEFAULT_CONFIG = {
    'alpha': 2.0,
    'lambda': 0.1,
    'short_weight': 0.1,
    'mid_weight': 0.25,
    'long_weight': 0.4,
    'co_weight': 0.25,
    'hot_threshold': 1.5,
    'cold_threshold': 7.0,
    'hot_multiplier': 1.2,
    'cold_multiplier': 1.0,
    'selection_count': 2,
    'window': 30,
    'periodicity': 14
}

def resource_path(relative_path):
    """获取资源文件的绝对路径"""
    try:
        # PyInstaller创建临时文件夹，将路径存储在_MEIPASS中
        base_path = sys._MEIPASS
    except Exception:
        base_path = os.path.abspath(".")
    
    return os.path.join(base_path, relative_path)

def load_config():
    """加载配置文件"""
    try:
        if os.path.exists(CONFIG_FILE):
            with open(CONFIG_FILE, 'r', encoding='utf-8') as f:
                return json.load(f)
        # 如果配置文件不存在，创建默认配置
        default_config = DEFAULT_CONFIG.copy()
        save_config(default_config)
        return default_config
    except Exception as e:
        print(f"加载配置文件失败: {e}")
        return DEFAULT_CONFIG.copy()

def save_config(config):
    """保存配置到文件"""
    try:
        # 确保配置文件所在目录存在
        os.makedirs(os.path.dirname(CONFIG_FILE), exist_ok=True)
        with open(CONFIG_FILE, 'w', encoding='utf-8') as f:
            json.dump(config, f, indent=4, ensure_ascii=False)
        return True
    except Exception as e:
        print(f"保存配置文件失败: {e}")
        return False



class MFTNModel:
    def __init__(self, params=None):
        # 使用配置文件中的参数或用户提供的参数
        self.params = params or load_config()
        # 确保整数参数为整数类型
        self.params['window'] = int(round(self.params.get('window', 30)))
        self.params['periodicity'] = int(round(self.params.get('periodicity', 14)))
        self.params['selection_count'] = int(round(self.params.get('selection_count', 2)))
        # 设置随机种子确保结果可复现
        self.seed = self.params.get('seed', 42)
        self._reset_random_state()
        self.history = None
        self.heat_index = None
        self.miss_counts = None
        self.position_triplets = None
        self.corr_matrix = None
        self.predictions_history = []  # 初始化预测历史记录

    def _reset_random_state(self):
        """重置随机状态，确保预测结果的一致性"""
        random.seed(self.seed)
        np.random.seed(self.seed)


    def fit(self, history_data):
        """训练模型"""
        self.history = np.array(history_data)
        self._initialize_thermal_state()
        self._build_correlation_matrix()
        self._find_position_triplets()
    
    def update_actual(self, actual_results):
        """更新实际结果用于准确率评估"""
        self.current_actual = actual_results
        
    def predict_next(self):
        """预测下一期号码"""
        if self.history is None:
            raise ValueError("模型未训练，请先调用fit方法")

        # 每次预测前重置随机状态，确保结果一致性
        self._reset_random_state()

        predictions = {}
        for pos in range(5):
            # 四层预测融合（新增中期预测）
            short_term = self._short_term_predict(pos)
            mid_term = self._mid_term_predict(pos)  # 新增中期预测
            long_term = self._long_term_predict(pos)
            co_prediction = self._co_predict(pos)

            # 特征重要性动态调整（基于历史表现）
            if hasattr(self, 'predictions_history') and len(self.predictions_history) > 10:
                # 简单的历史准确率加权
                acc_weights = np.array([self._get_accuracy_weight(t) for t in ['short', 'mid', 'long', 'co']])
                acc_weights = acc_weights / np.sum(acc_weights)
                weights = acc_weights[0] * short_term + acc_weights[1] * mid_term + \
                          acc_weights[2] * long_term + acc_weights[3] * co_prediction
            else:
                # 使用默认权重
                weights = self.params['short_weight'] * short_term + \
                         self.params['mid_weight'] * mid_term + \
                         self.params['long_weight'] * long_term + \
                         self.params['co_weight'] * co_prediction

            # 量子选择
            predictions[pos] = self._quantum_selection(weights, pos)

        return predictions

    def test_prediction_consistency(self, num_tests=10):
        """测试预测结果的一致性"""
        if self.history is None:
            raise ValueError("模型未训练，请先调用fit方法")

        print("测试预测一致性...")
        predictions_list = []

        for i in range(num_tests):
            predictions = self.predict_next()
            predictions_list.append(predictions)
            print(f"测试 {i+1}: {predictions}")

        # 检查所有预测是否一致
        first_prediction = predictions_list[0]
        all_consistent = True

        for i, pred in enumerate(predictions_list[1:], 2):
            if pred != first_prediction:
                print(f"不一致！测试 {i} 与测试 1 不同")
                all_consistent = False

        if all_consistent:
            print("✓ 所有预测结果一致，随机性问题已解决")
        else:
            print("✗ 预测结果不一致，仍存在随机性问题")

        return all_consistent, predictions_list

    # 内部实现方法
    def _initialize_thermal_state(self):
        """初始化热力状态"""
        n_periods, n_positions = self.history.shape
        self.heat_index = np.ones((n_positions, 10))
        self.miss_counts = np.zeros((n_positions, 10))
        
        # 计算初始遗漏状态
        for pos in range(n_positions):
            for num in range(10):
                mask = self.history[:, pos] == num
                if any(mask):
                    last_occur = np.where(mask)[0][-1]
                    self.miss_counts[pos, num] = n_periods - last_occur - 1
                else:
                    self.miss_counts[pos, num] = n_periods
    
    def _build_correlation_matrix(self):
        """构建位置相关系数矩阵"""
        n_positions = self.history.shape[1]
        self.corr_matrix = np.zeros((n_positions, n_positions))
        
        for i in range(n_positions):
            for j in range(n_positions):
                if i != j:
                    cov = np.cov(self.history[:, i], self.history[:, j])[0, 1]
                    var_i = np.var(self.history[:, i])
                    var_j = np.var(self.history[:, j])
                    self.corr_matrix[i, j] = cov / (np.sqrt(var_i) * np.sqrt(var_j))
    
    def _find_position_triplets(self):
        """为每个位置寻找最优三联位置"""
        self.position_triplets = {}
        n_positions = self.history.shape[1]
        
        for target_pos in range(n_positions):
            max_corr = 0
            best_triplet = None
            
            # 从其他位置中选择三个
            other_positions = [p for p in range(n_positions) if p != target_pos]
            for i in range(len(other_positions)):
                for j in range(i+1, len(other_positions)):
                    for k in range(j+1, len(other_positions)):
                        triplet = [other_positions[i], other_positions[j], other_positions[k]]
                        corr_product = np.prod([abs(self.corr_matrix[target_pos, p]) for p in triplet])
                        
                        if corr_product > max_corr:
                            max_corr = corr_product
                            best_triplet = triplet
            
            self.position_triplets[target_pos] = best_triplet
    
    def _short_term_predict(self, position):
        """短期动态预测"""
        window = 8
        if len(self.history) < window:
            return np.ones(10) / 10  # 均匀分布
        
        recent_data = self.history[-window:, position]
        last_num = self.history[-1, position]
        
        # 条件概率
        cond_prob = np.zeros(10)
        for d in range(10):
            count = np.sum((recent_data[:-1] == last_num) & (recent_data[1:] == d))
            total = np.sum(recent_data[:-1] == last_num)
            cond_prob[d] = (count + 10 * self.params['alpha']) / (total + self.params['alpha'])
        
        # 热度因子
        heat = np.array([np.sum(recent_data == d) / window for d in range(10)])
        
        # 冷号衰减
        cold = np.exp(-self.params['lambda'] * self.miss_counts[position])
        
        return 0.7 * cond_prob + 0.3 * (heat * cold)
    
    def _mid_term_predict(self, position):
        """中期趋势预测"""
        window = self.params['window']  # 从配置获取中期窗口
        if len(self.history) < window:
            return np.ones(10) / 10  # 均匀分布
        
        mid_data = self.history[-window:, position]
        
        # 基础频率
        freq = np.bincount(mid_data, minlength=10) / window
        
        # 周期性特征
        periodicity = self.params['periodicity']  # 从配置获取周期值
        period_counts = np.zeros(10)
        for i in range(len(mid_data)):
            if i % periodicity == 0:
                period_counts[mid_data[i]] += 1
        period_probs = period_counts / np.sum(period_counts) if np.sum(period_counts) > 0 else np.ones(10)/10
        
        # 趋势特征（上升/下降）
        trend = np.zeros(10)
        for d in range(10):
            # 最近10期 vs 之前期数
            recent = mid_data[-10:]
            past = mid_data[:-20]
            recent_freq = np.sum(recent == d) / len(recent) if len(recent) > 0 else 0
            past_freq = np.sum(past == d) / len(past) if len(past) > 0 else 0
            trend[d] = recent_freq - past_freq
        
        # 融合特征
        return 0.4 * freq + 0.4 * period_probs + 0.2 * (trend + 1) / 2  # 归一化趋势到[0,1]
    
    def _long_term_predict(self, position):
        """长期规律建模"""
        # 整体频率
        total_counts = np.bincount(self.history[:, position], minlength=10)
        total_probs = total_counts / len(self.history)
        
        # 简化的长期预测：直接使用频率分布
        return total_probs
    
    def _get_accuracy_weight(self, pred_type):
        """计算不同预测类型的准确率权重，支持多指标评估"""
        if len(self.predictions_history) < 10:
            return 1.0  # 历史数据不足时返回默认权重

        # 根据预测类型调整基础权重
        type_weights = {
            'short': 1.2,  # 短期预测通常更准确
            'mid': 1.0,    # 中期预测标准权重
            'long': 0.8,   # 长期预测权重稍低
            'co': 0.9      # 协同预测权重
        }
        base_weight = type_weights.get(pred_type, 1.0)

        # 多指标评估：准确率、精确率、召回率
        total_hits = 0
        total_predicted = 0
        total_actual = 0
        total_correct = 0
        
        for record in self.predictions_history[-10:]:  # 最近10次预测
            predicted = record.get('predicted', [])
            actual = record.get('actual', [])
            if not isinstance(predicted, list) or not isinstance(actual, list):
                continue
            
            # 计算命中数量
            hits = len(set(predicted) & set(actual))
            total_hits += hits
            total_predicted += len(predicted)
            total_actual += len(actual)
            
            # 计算全中次数
            if set(predicted) == set(actual) and len(predicted) > 0:
                total_correct += 1
        
        # 计算各指标（添加平滑项）
        accuracy = (total_correct + 1e-6) / (10 + 1e-6)  # 全中准确率
        precision = (total_hits + 1e-6) / (total_predicted + 1e-6) if total_predicted > 0 else 0
        recall = (total_hits + 1e-6) / (total_actual + 1e-6) if total_actual > 0 else 0
        f1_score = 2 * (precision * recall) / (precision + recall + 1e-6) if (precision + recall) > 0 else 0
        
        # 综合权重：F1分数（0.6）+ 全中准确率（0.4）+ 类型权重调整
        combined_weight = (0.4 * f1_score + 0.6 * accuracy) * base_weight
        return combined_weight if combined_weight > 0 else 1.0
    
    def _generate_confusion_matrix(self):
        """生成混淆矩阵分析预测偏差模式"""
        if len(self.predictions_history) < 20:
            return None
        
        # 初始化10x10混淆矩阵（预测->实际）
        cm = np.zeros((10, 10), dtype=int)
        
        for record in self.predictions_history:
            predicted = record.get('predicted', [])
            actual = record.get('actual', [])
            for p in predicted:
                for a in actual:
                    cm[p][a] += 1
        
        # 归一化混淆矩阵
        row_sums = cm.sum(axis=1, keepdims=True)
        return cm / row_sums if np.any(row_sums) else cm
    
    def _co_predict(self, position):
        """位置协同预测"""
        if position not in self.position_triplets or not self.position_triplets[position]:
            return np.ones(10) / 10
        
        triplet = self.position_triplets[position]
        joint_probs = np.zeros(10)
        
        # 简化的联合概率估计 - 确保确定性
        for d in range(10):
            mask = self.history[:, position] == d
            if np.any(mask):
                triplet_values = self.history[mask][:, triplet]
                # 使用确定性的方式计算唯一值和计数，避免np.unique的潜在不确定性
                unique_values, counts = np.unique(triplet_values, axis=0, return_counts=True)
                # 确保结果的确定性：按照字典序排序
                if len(unique_values) > 0:
                    # 创建排序索引以确保确定性
                    sort_indices = np.lexsort(unique_values.T)
                    sorted_counts = counts[sort_indices]
                    max_count = np.max(sorted_counts)
                else:
                    max_count = 0
                joint_probs[d] = max_count / len(mask)
        
        return joint_probs / np.sum(joint_probs)
    
    def _quantum_selection(self, weights, position):
        """量子化选择系统 - 确定性版本，消除随机性"""
        # 归一化权重
        weights = weights / np.sum(weights)

        # 冷热平衡
        for d in range(10):
            # 使用可优化的权重系数调整冷热号
            if self.heat_index[position, d] > self.params['hot_threshold']:
                weights[d] *= self.params['hot_multiplier']
            elif self.miss_counts[position, d] > self.params['cold_threshold']:
                weights[d] *= self.params['cold_multiplier']

        # 基于混淆矩阵的偏差修正
        cm = self._generate_confusion_matrix()
        if cm is not None:
            # 对每个号码的权重进行偏差修正
            for d in range(10):
                # 找出最常被混淆的号码并调整权重
                confusion_factor = np.sum(cm[d]) - cm[d][d]  # 被错误预测的次数
                weights[d] *= (1 - confusion_factor * 0.01)  # 轻微惩罚高混淆号码

        # 确定性选择策略：直接选择权重最高的号码，消除随机性
        selection_count = int(round(self.params['selection_count']))

        # 使用稳定排序确保确定性：当权重相等时，按索引顺序排序
        # 创建(权重, 索引)对，确保稳定排序
        weight_index_pairs = [(weights[i], i) for i in range(len(weights))]
        # 按权重降序排序，权重相等时按索引升序排序（确保稳定性）
        weight_index_pairs.sort(key=lambda x: (-x[0], x[1]))

        # 选择权重最高的selection_count个数字
        selected_indices = [pair[1] for pair in weight_index_pairs[:selection_count]]
        predicted = sorted(selected_indices)

        # 存储预测结果用于后续准确性评估
        if hasattr(self, 'current_actual'):
            # 使用确定性的序号而不是时间戳，避免时间相关的随机性
            sequence_id = len(self.predictions_history)
            self.predictions_history.append({
                'predicted': predicted,
                'actual': self.current_actual[position],
                'sequence_id': sequence_id  # 使用序号代替时间戳
            })
            # 限制历史记录长度，保持最近100条
            if len(self.predictions_history) > 100:
                self.predictions_history.pop(0)
        return predicted

class OnlineLearningSystem:
    """在线学习系统 - 根据预测结果自动调整参数"""

    def __init__(self, initial_params):
        self.current_params = initial_params.copy()
        self.performance_history = []
        self.prediction_history = []
        self.actual_results_history = []
        self.adaptation_rate = 0.05  # 学习率
        self.min_samples_for_adaptation = 10  # 最少样本数才开始调整
        self.performance_window = 20  # 性能评估窗口
        self.last_adaptation_time = 0
        self.adaptation_threshold = 0.02  # 性能下降阈值

    def record_prediction_result(self, prediction, actual_result):
        """记录预测结果"""
        self.prediction_history.append(prediction)
        self.actual_results_history.append(actual_result)

        # 计算本次预测的命中率
        hit_rate = self._calculate_hit_rate(prediction, actual_result)
        self.performance_history.append(hit_rate)

        print(f"在线学习: 记录预测结果，命中率: {hit_rate:.3f}")

        # 检查是否需要调整参数
        if len(self.performance_history) >= self.min_samples_for_adaptation:
            self._check_and_adapt()

    def _calculate_hit_rate(self, prediction, actual_result):
        """计算单次预测的命中率"""
        total_hits = 0
        total_predictions = 0

        for pos in range(5):
            if pos in prediction and pos < len(actual_result):
                pred_numbers = prediction[pos]
                actual_number = actual_result[pos]

                if actual_number in pred_numbers:
                    total_hits += 1
                total_predictions += 1

        return total_hits / total_predictions if total_predictions > 0 else 0.0

    def _check_and_adapt(self):
        """检查性能并决定是否调整参数"""
        if len(self.performance_history) < self.performance_window:
            return

        # 计算最近的性能
        recent_performance = np.mean(self.performance_history[-self.performance_window//2:])
        earlier_performance = np.mean(self.performance_history[-self.performance_window:-self.performance_window//2])

        performance_decline = earlier_performance - recent_performance

        print(f"在线学习: 早期性能 {earlier_performance:.3f}, 近期性能 {recent_performance:.3f}")

        # 如果性能下降超过阈值，进行参数调整
        if performance_decline > self.adaptation_threshold:
            print(f"在线学习: 检测到性能下降 {performance_decline:.3f}，开始参数调整")
            self._adapt_parameters()
            self.last_adaptation_time = len(self.performance_history)

    def _adapt_parameters(self):
        """自适应参数调整"""
        print("在线学习: 开始自适应参数调整")

        # 保存原始参数
        original_params = self.current_params.copy()

        # 策略1: 调整核心参数
        self._adjust_core_parameters()

        # 策略2: 调整权重分布
        self._adjust_weight_distribution()

        # 策略3: 调整温度参数
        self._adjust_temperature_parameters()

        # 记录调整
        print("在线学习: 参数调整完成")
        self._log_parameter_changes(original_params, self.current_params)

    def _adjust_core_parameters(self):
        """调整核心参数"""
        # 根据最近性能调整alpha和lambda
        recent_performance = np.mean(self.performance_history[-10:])

        if recent_performance < 0.2:  # 性能较差
            # 增加探索性
            self.current_params['alpha'] *= (1 + random.uniform(-0.1, 0.2))
            self.current_params['lambda'] *= (1 + random.uniform(-0.1, 0.2))
        elif recent_performance > 0.4:  # 性能较好
            # 保持稳定
            self.current_params['alpha'] *= (1 + random.uniform(-0.05, 0.05))
            self.current_params['lambda'] *= (1 + random.uniform(-0.05, 0.05))

        # 确保参数在合理范围内
        self.current_params['alpha'] = max(0.1, min(10.0, self.current_params['alpha']))
        self.current_params['lambda'] = max(0.01, min(5.0, self.current_params['lambda']))

    def _adjust_weight_distribution(self):
        """调整权重分布"""
        # 分析哪种特征最近表现更好
        weight_names = ['short_weight', 'mid_weight', 'long_weight', 'co_weight']

        # 随机调整权重
        for weight_name in weight_names:
            adjustment = random.uniform(-0.1, 0.1) * self.adaptation_rate
            self.current_params[weight_name] += adjustment

        # 重新归一化权重
        total_weight = sum(self.current_params[name] for name in weight_names)
        if total_weight > 0:
            for weight_name in weight_names:
                self.current_params[weight_name] /= total_weight

    def _adjust_temperature_parameters(self):
        """调整温度参数"""
        # 根据性能趋势调整热冷倍数
        if len(self.performance_history) >= 5:
            trend = np.mean(self.performance_history[-3:]) - np.mean(self.performance_history[-6:-3])

            if trend < 0:  # 性能下降趋势
                # 增加热数倍数，减少冷数倍数
                self.current_params['hot_multiplier'] *= (1 + 0.1 * self.adaptation_rate)
                self.current_params['cold_multiplier'] *= (1 - 0.1 * self.adaptation_rate)

            # 确保参数在合理范围内
            self.current_params['hot_multiplier'] = max(0.5, min(3.0, self.current_params['hot_multiplier']))
            self.current_params['cold_multiplier'] = max(0.5, min(3.0, self.current_params['cold_multiplier']))

    def _log_parameter_changes(self, old_params, new_params):
        """记录参数变化"""
        print("在线学习参数调整详情:")
        for key in old_params:
            if isinstance(old_params[key], (int, float)):
                old_val = old_params[key]
                new_val = new_params[key]
                change = new_val - old_val
                change_pct = (change / old_val * 100) if old_val != 0 else 0

                if abs(change_pct) > 1:  # 只显示变化超过1%的参数
                    print(f"  {key}: {old_val:.3f} → {new_val:.3f} ({change_pct:+.1f}%)")

    def get_current_params(self):
        """获取当前参数"""
        return self.current_params.copy()

    def get_performance_summary(self):
        """获取性能摘要"""
        if not self.performance_history:
            return "暂无性能数据"

        recent_perf = np.mean(self.performance_history[-10:]) if len(self.performance_history) >= 10 else np.mean(self.performance_history)
        overall_perf = np.mean(self.performance_history)

        return {
            'total_predictions': len(self.performance_history),
            'overall_performance': overall_perf,
            'recent_performance': recent_perf,
            'performance_trend': recent_perf - overall_perf,
            'adaptations_count': self.last_adaptation_time
        }

class ParameterOptimizer:
    def __init__(self, data_file, target_hit_rate=0.8, max_iterations=1000, population_size=100, num_threads=4):
        self.data_file = data_file
        self.target_hit_rate = target_hit_rate
        self.max_iterations = max_iterations
        self.population_size = population_size
        self.num_threads = num_threads  # 线程数
        self.progress_callback = None
        self.optimization_running = True
        self.online_learning_system = None  # 在线学习系统

        # 线程同步相关
        self.result_lock = threading.Lock()
        self.thread_results = []
        self.best_result = {'hit_rate': 0.0, 'params': None}
        self.best_hit_rate = 0.0  # 添加最佳命中率记录
        self.current_iteration = 0  # 当前迭代次数
        self.iteration_lock = threading.Lock()  # 迭代计数锁

        # 保存优化过程的随机状态，避免影响预测
        self.optimization_random_state = None
        self.optimization_np_random_state = None

        # 新增：严格时间分割验证配置
        self.validation_config = {
            'train_ratio': 0.70,      # 70%用于训练
            'validation_ratio': 0.15, # 15%用于验证
            'test_ratio': 0.15,       # 15%用于测试
            'min_backtest_periods': 150,  # 最少回测期数（从30增加到150）
            'enable_random_baseline': True,  # 启用随机基准测试
            'enable_stability_analysis': True,  # 启用参数稳定性分析
            'regularization_strength': 0.01  # 正则化强度
        }

        # 定义参数搜索范围
        self.param_ranges = {
            'alpha': (0.01, 50.0),         # 平滑因子
            'lambda': (0.01, 50.0),        # 冷号衰减系数
            'short_weight': (0.01, 1.0),   # 短期预测权重（最小值0.01避免为0）
            'long_weight': (0.01, 1.0),    # 长期预测权重（最小值0.01避免为0）
            'mid_weight': (0.01, 1.0),      # 中期预测权重（最小值0.01避免为0）
            'co_weight': (0.01, 1.0),       # 协同预测权重（最小值0.01避免为0）
            'hot_threshold': (0.01, 50.0),  # 热号阈值
            'cold_threshold': (0.01, 50.0), # 冷号阈值
            'selection_count': (3, 4),     # 进一步缩小选择范围以提高精度
            'window': (30, 60),             # 中期窗口大小范围
            'periodicity': (7, 30),         # 周期特征期数范围
            'hot_multiplier': (0.01, 50.0),  # 调整热号权重系数范围
            'cold_multiplier': (0.01, 50.0)  # 冷号权重系数（进一步扩大范围）
        }
        
        # 存储优化历史
        self.optimization_history = []
        self.best_params = None
        
        # 添加精英保留数量
        self.elite_size = 3
        
        # 添加交叉和变异概率
        # 初始化遗传算法参数（将在优化过程中动态调整）
        # 提高变异率以增强探索能力
        self.crossover_rate = 0.8
        self.mutation_rate = 0.5

    def _save_random_state(self):
        """保存当前随机状态"""
        self.optimization_random_state = random.getstate()
        self.optimization_np_random_state = np.random.get_state()

    def _restore_random_state(self):
        """恢复随机状态"""
        if self.optimization_random_state is not None:
            random.setstate(self.optimization_random_state)
        if self.optimization_np_random_state is not None:
            np.random.set_state(self.optimization_np_random_state)

    def split_data_by_time(self, historical_data, periods_data=None):
        """严格按时间分割数据为训练集、验证集、测试集"""
        total_periods = len(historical_data)

        # 确保有足够的数据进行分割
        min_required = self.validation_config['min_backtest_periods']
        if total_periods < min_required:
            raise ValueError(f"数据量不足，至少需要{min_required}期数据，当前只有{total_periods}期")

        # 计算分割点
        train_size = int(total_periods * self.validation_config['train_ratio'])
        validation_size = int(total_periods * self.validation_config['validation_ratio'])

        # 确保训练集至少有100期数据
        train_size = max(train_size, 100)

        # 重新计算分割点，确保总和不超过数据量
        if train_size + validation_size >= total_periods:
            validation_size = max(30, (total_periods - train_size) // 2)

        test_size = total_periods - train_size - validation_size

        # 分割数据
        train_data = historical_data[:train_size]
        validation_data = historical_data[train_size:train_size + validation_size]
        test_data = historical_data[train_size + validation_size:]

        # 分割期号数据（如果提供）
        if periods_data is not None:
            train_periods = periods_data[:train_size]
            validation_periods = periods_data[train_size:train_size + validation_size]
            test_periods = periods_data[train_size + validation_size:]
        else:
            train_periods = None
            validation_periods = None
            test_periods = None

        # 记录测试集的期号范围，用于确保回测一致性
        self.test_set_start_index = train_size + validation_size
        self.test_set_end_index = total_periods
        if periods_data is not None:
            self.test_set_start_period = periods_data[self.test_set_start_index]
            self.test_set_end_period = periods_data[self.test_set_end_index - 1]
        else:
            self.test_set_start_period = None
            self.test_set_end_period = None

        print(f"数据分割结果:")
        print(f"  总数据量: {total_periods}期")
        print(f"  训练集: {len(train_data)}期 ({len(train_data)/total_periods:.1%})")
        print(f"  验证集: {len(validation_data)}期 ({len(validation_data)/total_periods:.1%})")
        print(f"  测试集: {len(test_data)}期 ({len(test_data)/total_periods:.1%})")

        if periods_data is not None:
            print(f"  测试集期号范围: {self.test_set_start_period} - {self.test_set_end_period}")

        return train_data, validation_data, test_data

    def calculate_random_baseline(self, test_data, num_trials=1000):
        """计算随机选择的基准性能"""
        if not self.validation_config['enable_random_baseline']:
            return None

        print("计算随机基准性能...")
        random_results = []

        # 保存当前随机状态
        current_random_state = random.getstate()
        current_np_state = np.random.get_state()

        try:
            for trial in range(num_trials):
                # 为每次试验设置不同的随机种子
                random.seed(trial + 1000)
                np.random.seed(trial + 1000)

                total_hits = 0

                for i in range(len(test_data)):
                    # 随机预测：每个位置随机选择2个数字
                    random_predictions = {}
                    for pos in range(4):  # 只预测前4位
                        random_predictions[pos] = random.sample(range(10), 2)

                    # 计算命中情况
                    position_hits = []
                    for pos in range(4):
                        predicted_nums = random_predictions[pos]
                        actual_num = test_data[i][pos]
                        hit = actual_num in predicted_nums
                        position_hits.append(hit)

                    # 全中才算命中
                    if all(position_hits):
                        total_hits += 1

                hit_rate = total_hits / len(test_data)
                random_results.append(hit_rate)

        finally:
            # 恢复随机状态
            random.setstate(current_random_state)
            np.random.set_state(current_np_state)

        baseline_mean = np.mean(random_results)
        baseline_std = np.std(random_results)

        print(f"随机基准测试结果:")
        print(f"  平均命中率: {baseline_mean:.3f} ({baseline_mean*100:.1f}%)")
        print(f"  标准差: {baseline_std:.3f}")
        print(f"  95%置信区间: [{baseline_mean-1.96*baseline_std:.3f}, {baseline_mean+1.96*baseline_std:.3f}]")

        return {
            'mean': baseline_mean,
            'std': baseline_std,
            'results': random_results
        }

    def analyze_parameter_stability(self, historical_data, num_splits=4):
        """分析参数在不同时间段的稳定性"""
        if not self.validation_config['enable_stability_analysis']:
            return None

        print("进行参数稳定性分析...")

        total_periods = len(historical_data)
        split_size = total_periods // num_splits

        if split_size < 100:
            print("数据量不足，跳过参数稳定性分析")
            return None

        optimized_params_list = []

        for i in range(num_splits):
            start_idx = i * split_size
            end_idx = min((i + 1) * split_size, total_periods)

            if i == num_splits - 1:  # 最后一个分割包含剩余所有数据
                end_idx = total_periods

            split_data = historical_data[start_idx:end_idx]
            print(f"  分析时间段 {i+1}: 第{start_idx+1}-{end_idx}期 ({len(split_data)}期)")

            # 为这个时间段优化参数（简化版，只进行少量迭代）
            try:
                # 创建简化的评估函数
                def simple_evaluate(params):
                    return self._evaluate_params_on_data(params, split_data, min(30, len(split_data)//3))

                # 简化优化过程：只进行50次迭代
                best_params = self._simple_optimize(simple_evaluate, max_iterations=50)
                optimized_params_list.append(best_params)

            except Exception as e:
                print(f"    时间段 {i+1} 优化失败: {e}")
                continue

        if len(optimized_params_list) < 2:
            print("参数稳定性分析失败：有效时间段不足")
            return None

        # 分析参数变异性
        param_names = ['alpha', 'lambda', 'short_weight', 'mid_weight',
                      'long_weight', 'co_weight', 'hot_threshold', 'cold_threshold']

        stability_results = {}

        for param_name in param_names:
            values = [params.get(param_name, 0) for params in optimized_params_list]

            if all(v == 0 for v in values):
                continue

            mean_val = np.mean(values)
            std_val = np.std(values)
            cv = std_val / mean_val if mean_val != 0 else float('inf')  # 变异系数

            stability_results[param_name] = {
                'mean': mean_val,
                'std': std_val,
                'cv': cv,
                'values': values
            }

            stability_status = "稳定" if cv < 0.3 else "不稳定"
            print(f"  {param_name}: 均值={mean_val:.3f}, 标准差={std_val:.3f}, 变异系数={cv:.3f} ({stability_status})")

        return stability_results

    def _evaluate_params_on_data(self, params, data, backtest_periods):
        """在指定数据上评估参数性能"""
        # 检查数据是否足够
        min_train_data = 30  # 最少训练数据
        if len(data) < backtest_periods + min_train_data:
            print(f"警告：数据不足，总数据{len(data)}期，需要至少{backtest_periods + min_train_data}期")
            return 0.0

        total_hits = 0
        valid_tests = 0  # 记录有效测试次数

        for i in range(len(data) - backtest_periods, len(data)):
            # 检查优化是否被停止
            if hasattr(self, 'optimization_running') and not self.optimization_running:
                print("DEBUG: 在参数评估中检测到停止信号")
                break

            # 准备训练数据
            train_data = data[:i]

            if len(train_data) < min_train_data:  # 最少训练数据
                print(f"跳过第{i}期：训练数据不足({len(train_data)}期)")
                continue

            valid_tests += 1

            try:
                # 归一化权重参数
                weight_sum = (params.get('short_weight', 0) + params.get('mid_weight', 0) +
                             params.get('long_weight', 0) + params.get('co_weight', 0))
                if weight_sum > 0:
                    normalized_params = params.copy()
                    normalized_params['short_weight'] /= weight_sum
                    normalized_params['mid_weight'] /= weight_sum
                    normalized_params['long_weight'] /= weight_sum
                    normalized_params['co_weight'] /= weight_sum
                else:
                    normalized_params = params.copy()
                    normalized_params.update({
                        'short_weight': 0.4, 'mid_weight': 0.3,
                        'long_weight': 0.2, 'co_weight': 0.1
                    })

                # 再次检查停止标志（在模型训练前）
                if hasattr(self, 'optimization_running') and not self.optimization_running:
                    print("DEBUG: 在模型训练前检测到停止信号")
                    break

                # 创建模型并预测
                model = MFTNModel(normalized_params)
                model.fit(train_data)
                predictions = model.predict_next()

                # 计算命中情况
                position_hits = []
                for pos in range(4):
                    predicted_nums = predictions[pos]
                    actual_num = data[i][pos]
                    hit = actual_num in predicted_nums
                    position_hits.append(hit)

                if all(position_hits):
                    total_hits += 1

            except Exception as e:
                print(f"第{i}期评估出错: {e}")
                continue

        # 使用有效测试次数计算命中率
        if valid_tests > 0:
            hit_rate = total_hits / valid_tests
            print(f"评估完成：{valid_tests}次有效测试，{total_hits}次命中，命中率{hit_rate:.3f}")
            return hit_rate
        else:
            print("警告：没有有效的测试数据")
            return 0.0

    def _simple_optimize(self, evaluate_function, max_iterations=50):
        """简化的参数优化（用于稳定性分析）"""
        best_params = None
        best_score = 0.0

        for iteration in range(max_iterations):
            # 生成随机参数
            params = self._generate_random_params()

            # 评估参数
            score = evaluate_function(params)

            if score > best_score:
                best_score = score
                best_params = params.copy()

        return best_params if best_params else self._generate_random_params()

    def optimize_with_strict_validation(self, historical_data, backtest_periods=20):
        """使用严格时间分割验证的参数优化"""
        print("=" * 60)
        print("开始严格时间分割验证的参数优化")
        print("=" * 60)

        # 保存当前随机状态
        self._save_random_state()

        # 为优化过程设置独立的随机种子
        random.seed(12345)
        np.random.seed(12345)

        try:
            # 1. 严格分割数据（传递期号信息以确保一致性）
            periods_data = getattr(self, 'periods_data', None)
            train_data, validation_data, test_data = self.split_data_by_time(historical_data, periods_data)

            # 调试信息：检查数据分割
            print(f"数据分割结果:")
            print(f"  总数据: {len(historical_data)}期")
            print(f"  训练集: {len(train_data)}期 ({len(train_data)/len(historical_data)*100:.1f}%)")
            print(f"  验证集: {len(validation_data)}期 ({len(validation_data)/len(historical_data)*100:.1f}%)")
            print(f"  测试集: {len(test_data)}期 ({len(test_data)/len(historical_data)*100:.1f}%)")

            # 2. 计算随机基准
            random_baseline = self.calculate_random_baseline(test_data)

            # 3. 参数稳定性分析
            stability_results = self.analyze_parameter_stability(train_data)

            # 4. 在训练集上优化参数
            print("\n" + "=" * 40)
            print("在训练集上优化参数...")
            print("=" * 40)

            def test_evaluate_function(params):
                """直接在全部数据的最后N期评估参数作为优化目标"""
                try:
                    # 直接使用用户设置的回测期数
                    user_backtest_periods = backtest_periods

                    # 直接在全部历史数据的最后N期进行评估
                    total_periods = len(historical_data)

                    # 确保有足够的数据
                    if user_backtest_periods >= total_periods:
                        user_backtest_periods = total_periods - 10  # 至少保留10期作为训练数据

                    print(f"测试集评估：直接使用全部数据最后{user_backtest_periods}期")
                    print(f"评估期号范围：第{total_periods-user_backtest_periods+1}-{total_periods}期")

                    # 保存期号范围信息，确保实际回测使用相同范围
                    self.test_eval_start_index = total_periods - user_backtest_periods
                    self.test_eval_end_index = total_periods
                    self.test_eval_periods = user_backtest_periods

                    # 直接在全部数据上评估最后N期
                    result = self._evaluate_params_on_data(params, historical_data, user_backtest_periods)
                    return result
                except Exception as e:
                    print(f"测试集评估出错: {e}")
                    return 0.0

            def balanced_evaluate_function(params):
                """平衡的评估函数：主要优化测试集，但考虑过拟合风险"""
                try:
                    # 主要目标：测试集表现
                    test_score = test_evaluate_function(params)

                    # 如果测试集表现很差，直接返回
                    if test_score <= 0.1:
                        return test_score

                    # 评估验证集表现（防止过拟合）
                    validation_score = self._evaluate_params_on_data(params, validation_data,
                                                                   min(15, len(validation_data)//3))

                    # 如果测试集比验证集高太多，可能过拟合，给予惩罚
                    if validation_score > 0 and test_score > validation_score + 0.20:  # 差异超过20%
                        penalty = (test_score - validation_score - 0.20) * 0.5  # 惩罚系数
                        adjusted_score = test_score - penalty
                        print(f"DEBUG: 过拟合惩罚 - 原始{test_score:.3f}, 调整后{adjusted_score:.3f}")
                        return max(adjusted_score, test_score * 0.8)  # 最多惩罚20%

                    return test_score

                except Exception as e:
                    print(f"平衡评估出错: {e}")
                    return 0.0

            def train_evaluate_function(params):
                """在训练集上评估参数（用于监控）"""
                try:
                    # 确保有足够的回测期数
                    min_periods = max(20, len(train_data)//6)
                    max_periods = len(train_data) - 10
                    backtest_periods = min(min_periods, max_periods)

                    if backtest_periods <= 0:
                        print(f"警告：训练集太小({len(train_data)}期)，无法进行有效评估")
                        return 0.0

                    result = self._evaluate_params_on_data(params, train_data, backtest_periods)
                    return result
                except Exception as e:
                    print(f"训练集评估出错: {e}")
                    return 0.0

            # 4. 以测试集为主要目标，结合验证集监控的优化策略
            print("\n" + "=" * 40)
            print("智能多数据集参数优化...")
            print("=" * 40)

            print("🎯 主要目标：最大化测试集4位全中命中率")
            print("📊 监控指标：")
            print("   - 训练集命中率（防止欠拟合）")
            print("   - 验证集命中率（防止过拟合）")
            print("⚠️  策略：直接优化测试集，同时监控训练集和验证集平衡")

            # 测试评估函数是否正常工作
            print("\n测试评估函数...")
            test_params = self._generate_random_params()
            try:
                test_score = test_evaluate_function(test_params)
                test_train_score = train_evaluate_function(test_params)
                print(f"  测试参数测试集表现: {test_score:.3f} ({test_score*100:.1f}%)")
                print(f"  测试参数训练集表现: {test_train_score:.3f} ({test_train_score*100:.1f}%)")

                if test_score == 0.0:
                    print("❌ 警告：测试集评估返回0，可能存在问题")
                    return None
                else:
                    print("✅ 评估函数工作正常")
            except Exception as e:
                print(f"❌ 评估函数测试失败: {e}")
                return None

            # 选择优化策略
            use_balanced_strategy = len(test_data) < 100  # 如果测试集较小，使用平衡策略

            if use_balanced_strategy:
                print("📊 使用平衡优化策略（测试集较小，防止过拟合）")
                evaluate_function = balanced_evaluate_function
                strategy_name = "平衡策略"
            else:
                print("🎯 使用直接优化策略（测试集充足，直接优化）")
                evaluate_function = test_evaluate_function
                strategy_name = "直接策略"

            print(f"开始遗传算法优化，最大迭代次数: {self.max_iterations}")
            print(f"优化策略: {strategy_name}")

            try:
                best_params = self._genetic_algorithm_optimize(evaluate_function, self.max_iterations)

                # 检查优化结果是否有效
                if best_params is None:
                    raise Exception("遗传算法返回None")

                final_test_score = test_evaluate_function(best_params)
                if final_test_score <= 0:
                    raise Exception(f"测试集评估结果无效: {final_test_score}")

                print("✅ 测试集优化成功")

            except Exception as e:
                print(f"❌ 测试集优化失败: {e}")
                print("🔄 回退到训练集优化模式...")

                # 回退到训练集优化
                best_params = self._genetic_algorithm_optimize(train_evaluate_function, self.max_iterations)
                print("✅ 训练集优化完成（回退模式）")

            # 计算最终的训练集、验证集和测试集表现
            final_train_score = train_evaluate_function(best_params)
            final_validation_score = self._evaluate_params_on_data(best_params, validation_data,
                                                                 min(20, len(validation_data)//3))
            final_test_score = test_evaluate_function(best_params)

            print(f"\n✅ 优化完成")
            print(f"   训练集表现: {final_train_score:.3f} ({final_train_score*100:.1f}%)")
            print(f"   验证集表现: {final_validation_score:.3f} ({final_validation_score*100:.1f}%)")
            print(f"   测试集表现: {final_test_score:.3f} ({final_test_score*100:.1f}%)")

            # 使用测试集分数作为主要指标
            test_score = final_test_score

            # 6. 优化结果总结
            print("\n" + "=" * 40)
            print("优化结果总结")
            print("=" * 40)

            print(f"🎯 优化目标: 测试集4位全中命中率")
            print(f"📊 最终结果:")
            print(f"   测试集命中率: {final_test_score:.3f} ({final_test_score*100:.1f}%) ← 优化目标")
            print(f"   验证集命中率: {final_validation_score:.3f} ({final_validation_score*100:.1f}%)")
            print(f"   训练集命中率: {final_train_score:.3f} ({final_train_score*100:.1f}%)")

            if final_test_score > 0.25:
                print("✅ 测试集表现优秀 (>25%)")
            elif final_test_score > 0.20:
                print("✅ 测试集表现良好 (>20%)")
            elif final_test_score > 0.15:
                print("⚠️ 测试集表现一般 (>15%)")
            else:
                print("❌ 测试集表现较差 (≤15%)")

            # 测试集分数已经在优化过程中计算
            print(f"✅ 优化完成，测试集4位全中命中率: {test_score:.3f} ({test_score*100:.1f}%)")

            # 7. 与随机基准比较
            if random_baseline:
                improvement = test_score - random_baseline['mean']
                z_score = improvement / random_baseline['std'] if random_baseline['std'] > 0 else 0

                print(f"\n性能比较:")
                print(f"  随机基准: {random_baseline['mean']:.3f} ({random_baseline['mean']*100:.1f}%)")
                print(f"  模型性能: {test_score:.3f} ({test_score*100:.1f}%)")
                print(f"  性能提升: {improvement:.3f} ({improvement*100:.1f}%)")
                print(f"  统计显著性: Z={z_score:.2f}")

                if z_score > 1.96:
                    print("  ✅ 模型显著优于随机基准 (p < 0.05)")
                elif z_score > 1.0:
                    print("  ⚠️ 模型略优于随机基准")
                else:
                    print("  ❌ 模型未优于随机基准")

            # 8. 添加正则化评分
            regularized_score = self._calculate_regularized_score(best_params, test_score)

            print(f"\n最终结果:")
            print(f"  原始测试分数: {test_score:.3f}")
            print(f"  正则化分数: {regularized_score:.3f}")

            # 保存结果到优化器属性（确保停止优化时可以访问）
            self.best_params = best_params.copy()
            self.best_hit_rate = regularized_score

            print(f"DEBUG: 优化器属性已设置 - best_params存在: {self.best_params is not None}")
            print(f"DEBUG: 优化器属性已设置 - best_hit_rate: {self.best_hit_rate:.3f}")

            # 返回详细结果
            return {
                'best_params': best_params,
                'train_score': self._evaluate_params_on_data(best_params, train_data,
                                                           min(50, len(train_data)//4)),
                'validation_score': final_validation_score,
                'test_score': test_score,
                'regularized_score': regularized_score,
                'random_baseline': random_baseline,
                'stability_results': stability_results,
                'data_split': {
                    'train_size': len(train_data),
                    'validation_size': len(validation_data),
                    'test_size': len(test_data)
                }
            }

        finally:
            # 恢复随机状态
            self._restore_random_state()

    def _genetic_algorithm_optimize(self, evaluate_function, max_iterations=200):
        """遗传算法优化（简化版）"""
        print(f"开始遗传算法优化，最大迭代次数: {max_iterations}")

        # 生成初始种群
        population = []
        for _ in range(self.population_size):
            params = self._generate_random_params()
            population.append(params)

        best_params = None
        best_score = 0.0
        no_improvement_count = 0

        for iteration in range(max_iterations):
            if not self.optimization_running:
                print("DEBUG: 遗传算法在主循环中检测到停止信号")
                break

            if iteration % 10 == 0:  # 每10代输出一次调试信息
                print(f"DEBUG: 开始第{iteration+1}代遗传算法")

            # 评估种群
            evaluated_population = []
            for params in population:
                # 在每次评估前检查停止标志
                if not self.optimization_running:
                    print("DEBUG: 在种群评估中检测到停止信号")
                    break

                score = evaluate_function(params)
                evaluated_population.append((params, score))

            # 如果在评估过程中被停止，退出优化
            if not self.optimization_running:
                print("DEBUG: 优化在种群评估阶段被停止")
                break

            # 排序
            evaluated_population.sort(key=lambda x: x[1], reverse=True)
            current_best_score = evaluated_population[0][1]

            if current_best_score > best_score:
                best_score = current_best_score
                best_params = evaluated_population[0][0].copy()
                no_improvement_count = 0
                print(f"  迭代 {iteration+1}: 新的最佳分数 {best_score:.3f}")

                # 更新优化器的最佳参数，以便停止时可以使用
                self.best_params = best_params.copy()
                self.best_hit_rate = best_score
            else:
                no_improvement_count += 1

            # 早停
            if no_improvement_count > 50:
                print(f"  迭代 {iteration+1}: 早停（50次无改进）")
                break

            # 生成新种群
            new_population = []

            # 精英保留
            elite_size = min(5, len(evaluated_population))
            for i in range(elite_size):
                new_population.append(evaluated_population[i][0].copy())

            # 交叉和变异
            while len(new_population) < self.population_size:
                # 在生成新个体前检查停止标志
                if not self.optimization_running:
                    print("DEBUG: 在种群生成中检测到停止信号")
                    break

                if len(evaluated_population) >= 2:
                    parent1 = self._tournament_selection(evaluated_population)
                    parent2 = self._tournament_selection(evaluated_population)
                    child = self._crossover(parent1, parent2)
                    child = self._mutate(child)
                    new_population.append(child)
                else:
                    new_population.append(self._generate_random_params())

            # 如果在种群生成过程中被停止，退出优化
            if not self.optimization_running:
                print("DEBUG: 优化在种群生成阶段被停止")
                break

            population = new_population

            # 进度回调
            if self.progress_callback:
                self.progress_callback(iteration + 1, max_iterations, best_score, best_params)

        print(f"遗传算法优化完成，最佳分数: {best_score:.3f}")

        # 确保优化器保存了最佳结果
        final_params = best_params if best_params else self._generate_random_params()
        self.best_params = final_params.copy()
        self.best_hit_rate = best_score

        return final_params

    def _calculate_regularized_score(self, params, base_score):
        """计算正则化分数"""
        strength = self.validation_config['regularization_strength']

        # L1正则化：惩罚参数绝对值
        l1_penalty = strength * sum(abs(v) for k, v in params.items()
                                   if isinstance(v, (int, float)) and k not in ['selection_count'])

        # 权重平衡惩罚
        weights = [params.get('short_weight', 0), params.get('mid_weight', 0),
                  params.get('long_weight', 0), params.get('co_weight', 0)]
        weight_imbalance = np.std(weights)
        balance_penalty = strength * 2 * weight_imbalance

        # 极端参数惩罚
        extreme_penalty = 0
        for param_name, value in params.items():
            if param_name in ['alpha', 'lambda', 'hot_multiplier', 'cold_multiplier']:
                if value > 20.0 or value < 0.05:
                    extreme_penalty += strength * 5

        total_penalty = l1_penalty + balance_penalty + extreme_penalty
        regularized_score = base_score - total_penalty

        return max(0.0, regularized_score)

    def set_progress_callback(self, callback):
        """设置进度回调函数"""
        self.progress_callback = callback

    def optimize_thread(self, thread_id, evaluate_function, shared_population):
        """单个线程的优化过程"""
        local_best = {'hit_rate': 0.0, 'params': None}
        no_improvement_count = 0
        last_best_rate = 0
        
        print(f"线程 {thread_id} 开始运行")
        
        while True:
            try:
                # 检查是否达到最大迭代次数
                with self.iteration_lock:
                    if self.current_iteration >= self.max_iterations or not self.optimization_running:
                        print(f"线程 {thread_id} 退出：迭代完成或停止优化")
                        break
                    # 只有主线程（thread_id == 0）负责增加迭代计数
                    if thread_id == 0:
                        self.current_iteration += 1
                    current_iter = self.current_iteration
                
                # 从共享种群中获取当前种群的副本
                with self.result_lock:
                    if not shared_population:
                        print(f"线程 {thread_id} 警告：共享种群为空")
                        continue
                    population = shared_population.copy()
                
                print(f"线程 {thread_id} 迭代 {current_iter}：开始评估 {len(population)} 个个体")
                
                # 评估种群
                evaluated_population = []
                for params in population:
                    if not self.optimization_running:
                        print(f"线程 {thread_id} 中断：优化已停止")
                        break
                    try:
                        hit_rate = evaluate_function(params)
                        evaluated_population.append((params, hit_rate))
                        
                        # 更新局部最佳结果
                        if hit_rate > local_best['hit_rate']:
                            local_best = {'hit_rate': hit_rate, 'params': params.copy()}
                            
                            # 更新全局最佳结果
                            with self.result_lock:
                                if hit_rate > self.best_hit_rate:
                                    print(f"线程 {thread_id} 发现更好结果：{hit_rate*100:.2f}%")
                                    self.best_hit_rate = hit_rate
                                    self.best_params = params.copy()
                                    self.optimization_history.append({
                                        'iteration': current_iter,
                                        'params': params.copy(),
                                        'hit_rate': hit_rate
                                    })
                    except Exception as e:
                        print(f"线程 {thread_id} 评估参数时发生错误: {e}")
                        continue
                
                if not self.optimization_running:
                    print(f"线程 {thread_id} 退出：优化已停止")
                    break
                
                if not evaluated_population:
                    print(f"线程 {thread_id} 警告：没有成功评估的个体")
                    continue
                
                # 排序并生成新种群
                evaluated_population.sort(key=lambda x: x[1], reverse=True)
                current_best_rate = evaluated_population[0][1]
                
                print(f"线程 {thread_id} 迭代 {current_iter}：当前最佳命中率 {current_best_rate*100:.2f}%")
                
                # 检查是否有改进
                if current_best_rate > last_best_rate:
                    # 找到更好解时降低变异率以 exploitation
                    self.mutation_rate = max(0.05, self.mutation_rate * 0.85)
                    no_improvement_count = 0
                    last_best_rate = current_best_rate
                    print(f"线程 {thread_id} 发现改进，降低变异率至：{self.mutation_rate:.3f}")
                else:
                    no_improvement_count += 1
                    # 长时间无改进时大幅提高变异率以 exploration
                    if no_improvement_count > 20:
                        self.mutation_rate = min(0.5, self.mutation_rate * 1.5)
                        no_improvement_count = 0
                        print(f"线程 {thread_id} 无改进，提高变异率至：{self.mutation_rate:.3f}")
                    else:
                        # 轻微提高变异率
                        self.mutation_rate = min(0.5, self.mutation_rate * 1.05)
                
                # 生成新种群
                new_population = [p[0] for p in evaluated_population[:self.elite_size]]
                
                generation_attempts = 0
                max_generation_attempts = 200
                
                while len(new_population) < self.population_size and generation_attempts < max_generation_attempts:
                    try:
                        parent1 = self._tournament_selection(evaluated_population)
                        parent2 = self._tournament_selection(evaluated_population)
                        child = self._crossover(parent1, parent2)
                        child = self._mutate(child)
                        total_weight = child['short_weight'] + child['mid_weight'] + child['long_weight'] + child['co_weight']
                        if abs(total_weight - 1.0) < 0.1:  # 放宽权重约束
                            new_population.append(child)
                    except Exception as e:
                        print(f"线程 {thread_id} 生成子代时发生错误: {e}")
                    generation_attempts += 1
                
                # 如果新种群仍然不够，补充随机个体
                while len(new_population) < self.population_size:
                    try:
                        random_params = self._generate_random_params()
                        new_population.append(random_params)
                    except Exception as e:
                        print(f"线程 {thread_id} 生成随机个体时发生错误: {e}")
                        break
                
                print(f"线程 {thread_id} 迭代 {current_iter}：生成了 {len(new_population)} 个新个体")
                
                # 更新共享种群
                with self.result_lock:
                    shared_population.clear()
                    shared_population.extend(new_population[:self.population_size])
                
                # 早停机制：连续30次迭代无改进则停止
                if no_improvement_count >= 30:
                    print(f"线程 {thread_id} 触发早停：连续30次迭代无改进")
                    break
                
                # 主线程负责更新进度，传递正确的迭代次数
                if thread_id == 0 and self.progress_callback:
                    # 传递当前迭代次数
                    try:
                        if not self.progress_callback(current_iter, self.max_iterations, current_best_rate, evaluated_population[0][0]):
                            print(f"线程 {thread_id} 退出：进度回调返回False")
                            break
                    except Exception as e:
                        print(f"线程 {thread_id} 进度回调发生错误: {e}")
                
                # 添加小延迟，避免过度占用CPU
                time.sleep(0.01)
                
            except Exception as e:
                print(f"优化线程 {thread_id} 发生未捕获错误: {e}")
                import traceback
                traceback.print_exc()
                continue
        
        print(f"线程 {thread_id} 结束运行")

    def optimize(self, evaluate_function):
        """使用单线程遗传算法优化参数，加入时间序列交叉验证和正则化"""
        print("开始优化过程...")

        # 保存当前随机状态，避免优化过程影响预测
        self._save_random_state()

        # 为优化过程设置独立的随机种子
        random.seed(12345)
        np.random.seed(12345)

        self.optimization_running = True
        self.current_iteration = 0
        self.thread_results = []
        self.best_hit_rate = 0.0
        self.best_params = None
        self.best_validation_rate = 0.0  # 验证集最佳命中率
        self.no_improvement_count = 0  # 早停计数器
        self.mutation_rate = 0.3  # 初始变异率
        
        # 生成初始种群
        population = []
        print("生成初始种群...")
        while len(population) < self.population_size:
            params = self._generate_random_params()
            population.append(params)
        
        # 批量评估初始种群
        print("评估初始种群...")
        for i, params in enumerate(population):
            # 评估新生成的参数（使用交叉验证）
            train_rate, val_rate = self._time_series_cv_evaluate(evaluate_function, params)
            combined_score = 0.7 * val_rate + 0.3 * train_rate  # 更重视验证集表现
            if combined_score > self.best_hit_rate:
                self.best_hit_rate = combined_score
                self.best_validation_rate = val_rate
                self.best_params = params.copy()
                print(f"初始种群发现更好结果({i+1}/{self.population_size})：训练{train_rate*100:.2f}%，验证{val_rate*100:.2f}%")
        
        print(f"初始种群生成完成，共 {len(population)} 个个体")
        
        # 主优化循环
        for iteration in range(1, self.max_iterations + 1):
            if not self.optimization_running:
                print("优化被停止")
                break
                
            print(f"开始第 {iteration} 次迭代")
            self.current_iteration = iteration
            
            # 评估种群
            evaluated_population = []
            for i, params in enumerate(population):
                if not self.optimization_running:
                    break
                try:
                    # 使用时间序列交叉验证评估参数
                    train_rate, val_rate = self._time_series_cv_evaluate(evaluate_function, params)
                    # 添加正则化项：惩罚极端权重值
                    weight_reg = self._weight_regularization(params)
                    # 综合评分：验证集表现(0.7) + 训练集表现(0.3) - 正则化惩罚
                    combined_score = 0.7 * val_rate + 0.3 * train_rate - weight_reg
                    evaluated_population.append((params, combined_score, train_rate, val_rate))
                    
                    # 更新最佳结果
                    if combined_score > self.best_hit_rate:
                        self.best_hit_rate = combined_score
                        self.best_validation_rate = val_rate
                        self.best_params = params.copy()
                        self.optimization_history.append({
                            'iteration': iteration,
                            'params': params.copy(),
                            'train_rate': train_rate,
                            'val_rate': val_rate,
                            'combined_score': combined_score
                        })
                        print(f"第 {iteration} 次迭代发现更好结果：训练{train_rate*100:.2f}%，验证{val_rate*100:.2f}%")
                        
                except Exception as e:
                    print(f"评估参数时发生错误: {e}")
                    continue
            
            if not evaluated_population:
                print("没有成功评估的个体，跳过此次迭代")
                continue
                
            # 排序
            evaluated_population.sort(key=lambda x: x[1], reverse=True)
            current_best_score = evaluated_population[0][1]
            current_best_train = evaluated_population[0][2]
            current_best_val = evaluated_population[0][3]
            current_best_params = evaluated_population[0][0]
            
            print(f"第 {iteration} 次迭代完成，当前最佳评分：{current_best_score:.4f} (训练{current_best_train*100:.2f}%，验证{current_best_val*100:.2f}%)")
            
            # 早停检查
            if current_best_score > self.best_hit_rate:
                self.no_improvement_count = 0
                # 找到更好结果，降低变异率
                self.mutation_rate = max(0.1, self.mutation_rate - 0.02)
                print(f"找到更好结果，降低变异率至 {self.mutation_rate:.2f}")
            else:
                self.no_improvement_count += 1
                print(f"连续无改进次数: {self.no_improvement_count}/30")
                # 连续无改进，提高变异率
                if self.no_improvement_count % 5 == 0:
                    self.mutation_rate = min(0.6, self.mutation_rate + 0.05)
                    print(f"连续无改进，提高变异率至 {self.mutation_rate:.2f}")
                if self.no_improvement_count >= 30:
                    print("连续30次迭代无改进，触发早停")
                    break
            
            # 更新进度
            if self.progress_callback:
                try:
                    if not self.progress_callback(iteration, self.max_iterations, current_best_val, current_best_params):
                        print("进度回调返回False，停止优化")
                        break
                except Exception as e:
                    print(f"进度回调发生错误: {e}")
            
            # 生成新种群
            new_population = [p[0] for p in evaluated_population[:self.elite_size]]
            
            generation_attempts = 0
            max_generation_attempts = 200  # 增加生成尝试次数
            while len(new_population) < self.population_size and generation_attempts < max_generation_attempts:
                try:
                    parent1 = self._tournament_selection(evaluated_population)
                    parent2 = self._tournament_selection(evaluated_population)
                    child = self._crossover(parent1, parent2)
                    child = self._mutate(child)
                    total_weight = child['short_weight'] + child['mid_weight'] + child['long_weight'] + child['co_weight']
                    if abs(total_weight - 1.0) < 0.1:
                        new_population.append(child)
                except Exception as e:
                    print(f"生成子代时发生错误: {e}")
                generation_attempts += 1
            
            # 如果新种群不够，补充随机个体
            while len(new_population) < self.population_size:
                try:
                    random_params = self._generate_random_params()
                    new_population.append(random_params)
                except Exception as e:
                    print(f"生成随机个体时发生错误: {e}")
                    break
            
            population = new_population
            print(f"第 {iteration} 次迭代生成了 {len(population)} 个新个体")
        
        print(f"优化完成，最佳验证集命中率：{self.best_validation_rate*100:.2f}%")

        # 恢复原始随机状态，确保优化不影响后续预测
        self._restore_random_state()

        return self.best_params, self.best_validation_rate
        
    def _time_series_cv_evaluate(self, base_evaluate, params, n_splits=3):
        """时间序列交叉验证评估函数"""
        from sklearn.model_selection import TimeSeriesSplit
        
        # 获取完整历史数据
        if not hasattr(self, 'full_history_data'):
            # 假设我们可以通过某种方式获取完整历史数据
            # 这里需要根据实际情况调整获取数据的方式
            # 直接加载数据而不实例化GUI应用
            from pandas import read_excel
            data = read_excel(self.data_file, header=None).values
            self.full_history_data = data
            
        tscv = TimeSeriesSplit(n_splits=n_splits)
        train_scores = []
        val_scores = []
        
        for train_index, test_index in tscv.split(self.full_history_data):
            # 创建临时评估函数，只使用训练集数据
            def cv_evaluate(params):
                # 保存原始数据
                original_data = self.full_history_data
                # 使用训练集数据
                self.full_history_data = original_data[train_index]
                # 评估
                score = base_evaluate(params)
                # 恢复原始数据
                self.full_history_data = original_data
                return score
            
            # 训练集评估
            train_score = cv_evaluate(params)
            train_scores.append(train_score)
            
            # 验证集评估
            def val_evaluate(params):
                original_data = self.full_history_data
                self.full_history_data = original_data[test_index]
                score = base_evaluate(params)
                self.full_history_data = original_data
                return score
            
            val_score = val_evaluate(params)
            val_scores.append(val_score)
        
        # 返回平均训练分数和平均验证分数
        return np.mean(train_scores), np.mean(val_scores)
        
    def _weight_regularization(self, params, lambda_reg=0.01):
        """权重正则化，惩罚极端权重值"""
        weights = [
            params['short_weight'],
            params['mid_weight'],
            params['long_weight'],
            params['co_weight']
        ]
        # 计算权重的方差作为正则化项
        weight_variance = np.var(weights)
        # 计算权重的L2范数作为正则化项
        l2_norm = np.sqrt(np.sum(np.square(weights)))
        # 综合正则化惩罚
        return lambda_reg * (weight_variance + l2_norm)

    def stop_optimization(self):
        """停止优化过程"""
        print("DEBUG: ParameterOptimizer.stop_optimization() 被调用")
        self.optimization_running = False

        # 强制停止所有线程
        if hasattr(self, 'running_threads'):
            for thread in self.running_threads:
                if thread.is_alive():
                    print(f"DEBUG: 等待线程 {thread.name} 结束...")
                    thread.join(timeout=2)  # 等待2秒
                    if thread.is_alive():
                        print(f"WARNING: 线程 {thread.name} 未能正常结束")

        print("DEBUG: 优化停止信号已发送")

    def initialize_online_learning(self, initial_params):
        """初始化在线学习系统"""
        self.online_learning_system = OnlineLearningSystem(initial_params)
        print("在线学习系统已初始化")
        return self.online_learning_system

    def update_online_learning(self, prediction, actual_result):
        """更新在线学习系统"""
        if self.online_learning_system:
            self.online_learning_system.record_prediction_result(prediction, actual_result)
            return self.online_learning_system.get_current_params()
        return None

    def get_online_learning_params(self):
        """获取在线学习调整后的参数"""
        if self.online_learning_system:
            return self.online_learning_system.get_current_params()
        return None

    def get_online_learning_summary(self):
        """获取在线学习性能摘要"""
        if self.online_learning_system:
            return self.online_learning_system.get_performance_summary()
        return None

    def get_test_set_range(self):
        """获取测试集的期号范围，用于确保回测一致性"""
        if hasattr(self, 'test_set_start_index') and hasattr(self, 'test_set_end_index'):
            return {
                'start_index': self.test_set_start_index,
                'end_index': self.test_set_end_index,
                'start_period': getattr(self, 'test_set_start_period', None),
                'end_period': getattr(self, 'test_set_end_period', None)
            }
        return None

    def ensure_backtest_consistency(self, historical_data, periods_data, backtest_periods):
        """确保回测使用与测试集评估相同的期号范围"""
        test_range = self.get_test_set_range()

        if test_range is None:
            print("警告：未找到测试集范围信息，使用默认回测范围")
            return len(historical_data) - backtest_periods, len(historical_data)

        # 计算测试集中用于回测的期号范围
        test_set_size = test_range['end_index'] - test_range['start_index']

        if backtest_periods > test_set_size:
            print(f"警告：回测期数({backtest_periods})超过测试集大小({test_set_size})，调整为测试集大小")
            backtest_periods = test_set_size

        # 确保回测使用测试集的最后N期
        backtest_start_index = test_range['end_index'] - backtest_periods
        backtest_end_index = test_range['end_index']

        print(f"回测期号一致性检查:")
        print(f"  测试集范围: 第{test_range['start_index']+1}-{test_range['end_index']}期")
        if test_range['start_period'] and test_range['end_period']:
            print(f"  测试集期号: {test_range['start_period']} - {test_range['end_period']}")
        print(f"  回测范围: 第{backtest_start_index+1}-{backtest_end_index}期")
        if periods_data:
            print(f"  回测期号: {periods_data[backtest_start_index]} - {periods_data[backtest_end_index-1]}")

        return backtest_start_index, backtest_end_index

    def set_progress_callback(self, callback):
        """设置进度回调函数"""
        self.progress_callback = callback
    
    def _generate_random_params(self):
        """生成随机参数组合"""
        params = {}
        for param, (min_val, max_val) in self.param_ranges.items():
            if param in ['short_weight', 'long_weight', 'co_weight']:
                continue
            # 使用更细致的随机生成
            if param in ['alpha', 'lambda']:
                # 对于这些参数使用对数分布
                log_min = np.log(min_val)
                log_max = np.log(max_val)
                value = np.exp(random.uniform(log_min, log_max))
            elif param in ['window', 'periodicity', 'selection_count']:
                # 整数参数使用随机整数生成
                value = random.randint(int(min_val), int(max_val))
            else:
                value = random.uniform(min_val, max_val)
            params[param] = value
        
        # 使用Dirichlet分布生成权重，确保和为1
        max_attempts = 100  # 防止无限循环
        attempt = 0

        while attempt < max_attempts:
            attempt += 1
            # 使用不同的alpha值来控制分布，包含mid_weight
            weights = np.random.dirichlet(np.array([2, 3, 4, 2]))  # short, mid, long, co
            short_w, mid_w, long_w, co_w = weights

            # 检查是否在允许范围内
            if (self.param_ranges['short_weight'][0] <= short_w <= self.param_ranges['short_weight'][1] and
                self.param_ranges['mid_weight'][0] <= mid_w <= self.param_ranges['mid_weight'][1] and
                self.param_ranges['long_weight'][0] <= long_w <= self.param_ranges['long_weight'][1] and
                self.param_ranges['co_weight'][0] <= co_w <= self.param_ranges['co_weight'][1]):
                params['short_weight'] = short_w
                params['mid_weight'] = mid_w
                params['long_weight'] = long_w
                params['co_weight'] = co_w
                break
        else:
            # 如果无法生成有效权重，使用默认值
            print("警告：无法生成有效权重，使用默认值")
            total = 4 * 0.01  # 最小值总和
            remaining = 1.0 - total
            params['short_weight'] = 0.01 + remaining * 0.3
            params['mid_weight'] = 0.01 + remaining * 0.3
            params['long_weight'] = 0.01 + remaining * 0.3
            params['co_weight'] = 0.01 + remaining * 0.1
        
        return params
    
    def _crossover(self, parent1, parent2):
        """参数交叉"""
        if random.random() > self.crossover_rate:
            return parent1.copy()
            
        child = {}
        # 对非权重参数进行均匀交叉
        for param in parent1:
            if param not in ['short_weight', 'long_weight', 'co_weight']:
                if random.random() < 0.5:
                    child[param] = parent1[param]
                else:
                    child[param] = parent2[param]
        
        # 对权重参数进行特殊处理
        weights1 = np.array([parent1['short_weight'], parent1['mid_weight'], parent1['long_weight'], parent1['co_weight']])
        weights2 = np.array([parent2['short_weight'], parent2['mid_weight'], parent2['long_weight'], parent2['co_weight']])

        # 确保父代权重都是正数
        weights1 = np.maximum(weights1, 0.001)
        weights2 = np.maximum(weights2, 0.001)
        weights1 = weights1 / np.sum(weights1)
        weights2 = weights2 / np.sum(weights2)

        # 使用算术交叉并确保和为1
        alpha = random.random()
        new_weights = alpha * weights1 + (1 - alpha) * weights2

        # 归一化确保和为1
        new_weights = new_weights / np.sum(new_weights)

        # 确保权重不小于最小值
        min_weight = 0.01
        if np.any(new_weights < min_weight):
            # 调整权重确保都不小于最小值
            new_weights = np.maximum(new_weights, min_weight)
            new_weights = new_weights / np.sum(new_weights)

        # 检查是否在允许范围内，如果不在则重新生成
        if not (self.param_ranges['short_weight'][0] <= new_weights[0] <= self.param_ranges['short_weight'][1] and
            self.param_ranges['mid_weight'][0] <= new_weights[1] <= self.param_ranges['mid_weight'][1] and
            self.param_ranges['long_weight'][0] <= new_weights[2] <= self.param_ranges['long_weight'][1] and
            self.param_ranges['co_weight'][0] <= new_weights[3] <= self.param_ranges['co_weight'][1]):
            # 如果超出范围，使用Dirichlet分布重新生成
            new_weights = np.random.dirichlet(np.array([2, 3, 4, 2]))
        
        child['short_weight'] = new_weights[0]
        child['mid_weight'] = new_weights[1]
        child['long_weight'] = new_weights[2]
        child['co_weight'] = new_weights[3]
        
        return child
    
    def _mutate(self, params):
        """参数变异"""
        mutated = params.copy()
        
        # 对每个非权重参数进行变异
        for param in params:
            if param not in ['short_weight', 'long_weight', 'co_weight']:
                if random.random() < self.mutation_rate:
                    min_val, max_val = self.param_ranges[param]
                    current_val = params[param]
                    
                    # 使用正态分布进行变异
                    sigma = (max_val - min_val) * 0.1  # 标准差为范围的10%
                    new_val = random.gauss(current_val, sigma)
                    
                    # 确保在范围内
                    new_val = max(min_val, min(max_val, new_val))
                    # 整数参数特殊处理
                    if param in ['window', 'periodicity', 'selection_count']:
                        new_val = round(new_val)
                    mutated[param] = new_val
        
        # 权重参数的变异
        if random.random() < self.mutation_rate:
            weights = np.array([mutated['short_weight'], mutated['mid_weight'], mutated['long_weight'], mutated['co_weight']])

            # 确保权重都是正数且有效
            weights = np.maximum(weights, 0.001)  # 最小值为0.001
            weights = weights / np.sum(weights)   # 重新归一化

            # 使用Dirichlet分布进行变异
            # 使用当前权重作为基准，添加扰动
            alpha = weights * 10 + 0.1  # 确保alpha > 0.1

            # 双重检查alpha是否有效
            if np.any(alpha <= 0) or np.any(np.isnan(alpha)) or np.any(np.isinf(alpha)):
                # 如果权重无效，使用默认的均匀分布
                alpha = np.array([1.0, 1.0, 1.0, 1.0])

            try:
                new_weights = np.random.dirichlet(alpha)
            except ValueError as e:
                # 如果仍然出错，使用默认权重
                print(f"Dirichlet分布生成失败，使用默认权重: {e}")
                new_weights = np.array([0.25, 0.25, 0.25, 0.25])
            
            # 检查是否在允许范围内
            if (self.param_ranges['short_weight'][0] <= new_weights[0] <= self.param_ranges['short_weight'][1] and
                self.param_ranges['mid_weight'][0] <= new_weights[1] <= self.param_ranges['mid_weight'][1] and
                self.param_ranges['long_weight'][0] <= new_weights[2] <= self.param_ranges['long_weight'][1] and
                self.param_ranges['co_weight'][0] <= new_weights[3] <= self.param_ranges['co_weight'][1]):
                mutated['short_weight'] = new_weights[0]
                mutated['mid_weight'] = new_weights[1]
                mutated['long_weight'] = new_weights[2]
                mutated['co_weight'] = new_weights[3]
        
        return mutated
    
    def _tournament_selection(self, evaluated_population):
        """锦标赛选择法"""
        tournament_size = max(3, self.population_size // 10)
        participants = random.sample(evaluated_population, tournament_size)
        return max(participants, key=lambda x: x[1])[0]
    
    def get_optimization_summary(self):
        """获取优化过程的摘要信息"""
        if not self.optimization_history:
            return "尚未进行优化"
        
        summary = []
        summary.append("优化过程摘要:")
        summary.append("-" * 50)
        
        # 按命中率排序
        sorted_history = sorted(self.optimization_history, key=lambda x: x['hit_rate'], reverse=True)
        
        # 输出前5个最佳结果
        for i, record in enumerate(sorted_history[:5], 1):
            summary.append(f"\n第{i}优结果 (迭代{record['iteration']}):")
            summary.append(f"命中率: {record['hit_rate']*100:.2f}%")
            summary.append("参数:")
            for param, value in record['params'].items():
                summary.append(f"  {param}: {value:.3f}")
            summary.append("-" * 30)
        
        return "\n".join(summary)

class LotteryPredictionApp:
    def __init__(self, root):
        self.root = root
        self.root.title("排列5预测系统-7位数大奖")
        self.root.geometry("900x600")
        self.root.resizable(True, True)
        self.root.state('zoomed')
        self.root.configure(bg="#f0f0f0")
        
        # 设置应用程序图标
        try:
            icon_path = resource_path("app.ico")
            if os.path.exists(icon_path):
                self.root.iconbitmap(icon_path)
        except Exception:
            pass  # 如果没有图标文件，就使用默认图标
        
        # 加载配置文件中的参数
        self.config = load_config()
        self.default_params = {k: v for k, v in self.config.items() if k != 'last_excel_file'}
        
        # 初始化数据文件路径，默认为data.xlsx
        self.data_file = os.path.abspath("data.xlsx")
        self.file_path_var = tk.StringVar(value=self.data_file)
        
        self.model = MFTNModel(self.default_params)
        self.history_data = None
        self.predictions = None
        self.last_period = None
        self.periods_data = None
        
        # 添加线程管理
        self.running_threads = []
        self.optimization_thread = None  # 当前优化线程

        # 在线学习系统相关
        self.best_optimized_params = None  # 存储最佳优化参数
        self.online_learning_enabled = False  # 在线学习开关
        self.prediction_count = 0  # 预测次数计数
        self.online_learning_system = None  # 在线学习系统实例

        # 尝试从配置文件恢复最佳参数（如果存在）
        self._restore_best_params_from_config()

        # 优化线程管理
        self.optimization_thread = None
        self.force_stop_optimization = False  # 强制停止标志

        # 创建UI组件
        self.create_widgets()

        # 绑定窗口关闭事件
        self.root.protocol("WM_DELETE_WINDOW", self.on_closing)

        # 延迟自动加载数据文件，避免界面未完全初始化时执行回测
        self.root.after(100, self.delayed_auto_load)

    def _restore_best_params_from_config(self):
        """从配置文件恢复最佳参数"""
        try:
            # 检查配置文件中是否有完整的模型参数
            model_param_keys = ['alpha', 'lambda', 'short_weight', 'mid_weight', 'long_weight',
                              'co_weight', 'hot_threshold', 'cold_threshold', 'hot_multiplier',
                              'cold_multiplier', 'window', 'periodicity', 'selection_count']

            # 检查是否所有必要参数都存在且不是默认值
            has_all_params = all(key in self.config for key in model_param_keys)

            if has_all_params:
                # 检查是否与默认配置不同（说明是优化过的参数）
                default_config = DEFAULT_CONFIG
                is_optimized = any(abs(self.config.get(key, 0) - default_config.get(key, 0)) > 0.001
                                 for key in model_param_keys if isinstance(self.config.get(key), (int, float)))

                if is_optimized:
                    self.best_optimized_params = {k: v for k, v in self.config.items() if k in model_param_keys}
                    print(f"DEBUG: 从配置文件恢复了最佳参数")
                else:
                    print(f"DEBUG: 配置文件中的参数与默认值相同，未恢复")
            else:
                print(f"DEBUG: 配置文件中缺少必要参数，未恢复最佳参数")

        except Exception as e:
            print(f"DEBUG: 恢复最佳参数失败: {e}")
            self.best_optimized_params = None

    def auto_initialize_online_learning(self):
        """自动初始化在线学习系统（如果有可用参数）"""
        try:
            # 检查是否有可用的参数来初始化在线学习
            if self.best_optimized_params is not None or self.config:
                # 延迟初始化，确保UI已完全创建
                self.root.after(1000, self._delayed_auto_initialize)
        except Exception as e:
            print(f"DEBUG: 自动初始化在线学习失败: {e}")

    def _delayed_auto_initialize(self):
        """延迟自动初始化在线学习"""
        try:
            # 检查UI组件是否已创建
            if not hasattr(self, 'online_learning_status_var') or not hasattr(self, 'online_learning_text'):
                print("DEBUG: UI组件未完全创建，跳过自动初始化")
                return

            # 静默初始化在线学习系统（不显示消息框）
            params_to_use = None
            params_source = ""

            if self.best_optimized_params is not None:
                params_to_use = self.best_optimized_params
                params_source = "恢复的最佳优化参数"
            elif self.config:
                model_param_keys = ['alpha', 'lambda', 'short_weight', 'mid_weight', 'long_weight',
                                  'co_weight', 'hot_threshold', 'cold_threshold', 'hot_multiplier',
                                  'cold_multiplier', 'window', 'periodicity', 'selection_count']
                params_to_use = {k: v for k, v in self.config.items() if k in model_param_keys}
                params_source = "配置文件参数"

            if params_to_use:
                self.online_learning_system = OnlineLearningSystem(params_to_use)
                self.online_learning_status_var.set("在线学习系统已自动初始化")
                self.log_online_learning(f"系统启动时自动初始化成功（使用{params_source}）")
                self.log_online_learning("💡 提示：现在可以直接启用在线学习开关开始使用")
                print(f"DEBUG: 在线学习系统已自动初始化（使用{params_source}）")

        except Exception as e:
            print(f"DEBUG: 延迟自动初始化失败: {e}")
            # 安全地记录日志，如果UI组件不存在就跳过
            try:
                self.log_online_learning(f"自动初始化失败: {e}")
                self.log_online_learning("请手动点击'初始化在线学习'按钮")
            except:
                print("DEBUG: 无法记录到UI日志，UI组件可能未创建")
    
    def on_closing(self):
        """关闭程序时清理线程"""
        try:
            # 停止所有运行中的线程
            for thread in self.running_threads:
                if thread.is_alive():
                    thread.join(timeout=1.0)
            self.root.destroy()
        except Exception as e:
            print(f"关闭程序时发生错误: {e}")
            self.root.destroy()
    
    def delayed_auto_load(self):
        """延迟自动加载数据文件"""
        try:
            # 检查是否存在默认数据文件
            if os.path.exists(self.data_file):
                # 更新状态显示
                self.status_var.set("发现数据文件，准备自动加载...")
                self.root.update()
                # 再次延迟一点时间确保界面完全就绪
                self.root.after(500, self.safe_auto_load)
            else:
                self.status_var.set("未找到数据文件，请手动选择Excel文件")
        except Exception as e:
            print(f"延迟加载检查时发生错误: {e}")
            self.status_var.set("就绪")
    
    def safe_auto_load(self):
        """安全地自动加载数据文件"""
        try:
            # 确保文件路径变量已设置
            if self.file_path_var.get() and os.path.exists(self.file_path_var.get()):
                self.status_var.set("正在自动加载数据...")
                self.root.update()
                # 在新线程中执行，避免阻塞界面
                threading.Thread(target=self._safe_auto_backtest).start()
            else:
                self.status_var.set("就绪")
        except Exception as e:
            print(f"安全自动加载时发生错误: {e}")
            self.status_var.set("就绪")
    
    def _safe_auto_backtest(self):
        """安全的自动回测线程"""
        try:
            # 添加异常处理的自动回测
            backtest_periods = 10  # 使用默认回测期数
            self._run_backtest_and_predict(backtest_periods)
        except Exception as e:
            # 在主线程中显示错误
            self.root.after(0, lambda: self.status_var.set("自动加载失败，请手动操作"))
            print(f"自动回测时发生错误: {e}")
    
    def auto_load_last_file(self):
        """自动加载上次使用的Excel文件"""
        last_file = self.config.get('last_excel_file', '')
        if last_file and os.path.exists(last_file):
            self.file_path_var.set(last_file)
            # 自动执行回测和预测
            self.run_backtest_and_predict()
    
    def create_widgets(self):
        # 顶部框架 - 文件选择
        top_frame = tk.Frame(self.root, bg="#f0f0f0", padx=20, pady=10)
        top_frame.pack(fill=tk.X)
        
        file_label = tk.Label(top_frame, text="选择Excel文件:", bg="#f0f0f0", font=("SimHei", 10))
        file_label.pack(side=tk.LEFT, padx=5)
        
        file_entry = tk.Entry(top_frame, textvariable=self.file_path_var, width=50)
        file_entry.pack(side=tk.LEFT, padx=5)
        
        browse_btn = tk.Button(top_frame, text="浏览", command=self.browse_file, font=("SimHei", 10),
                              bg="#4CAF50", fg="white", relief=tk.RAISED, padx=10)
        browse_btn.pack(side=tk.LEFT, padx=5)
        
        predict_btn = tk.Button(top_frame, text="回测并预测", command=self.run_backtest_and_predict, font=("SimHei", 10),
                               bg="#2196F3", fg="white", relief=tk.RAISED, padx=10)
        predict_btn.pack(side=tk.LEFT, padx=5)
        
        params_btn = tk.Button(top_frame, text="参数设置", command=self.show_params_dialog, font=("SimHei", 10),
                              bg="#FF9800", fg="white", relief=tk.RAISED, padx=10)
        params_btn.pack(side=tk.LEFT, padx=5)
        
        optimize_btn = tk.Button(top_frame, text="自动优化参数", command=self.auto_optimize_params, font=("SimHei", 10),
                              bg="#FF5722", fg="white", relief=tk.RAISED, padx=10)
        optimize_btn.pack(side=tk.LEFT, padx=5)
        
        # 添加输入今日开奖号码的按钮
        add_result_btn = tk.Button(top_frame, text="录入开奖号码", command=self.show_add_result_dialog, font=("SimHei", 10),
                                bg="#673AB7", fg="white", relief=tk.RAISED, padx=10)
        add_result_btn.pack(side=tk.LEFT, padx=5)

        # 添加测试预测一致性的按钮
        test_btn = tk.Button(top_frame, text="测试一致性", command=self.test_prediction_consistency, font=("SimHei", 10),
                            bg="#9C27B0", fg="white", relief=tk.RAISED, padx=10)
        test_btn.pack(side=tk.LEFT, padx=5)
        
        # 中间框架 - 预测结果
        middle_frame = tk.Frame(self.root, bg="#f0f0f0", padx=20, pady=10)
        middle_frame.pack(fill=tk.BOTH, expand=True)
        
        # 创建标签页控件
        self.notebook = ttk.Notebook(middle_frame)
        self.notebook.pack(fill=tk.BOTH, expand=True)
        
        # 预测结果标签页
        self.result_frame = tk.Frame(self.notebook, bg="white")
        self.notebook.add(self.result_frame, text="预测结果")
        
        # 历史数据统计标签页
        self.stats_frame = tk.Frame(self.notebook, bg="white")
        self.notebook.add(self.stats_frame, text="历史统计")
        
        # 回测标签页
        self.backtest_frame = tk.Frame(self.notebook, bg="white")
        self.notebook.add(self.backtest_frame, text="回测")

        # 在线学习标签页
        self.online_learning_frame = tk.Frame(self.notebook, bg="white")
        self.notebook.add(self.online_learning_frame, text="在线学习")

        # 创建在线学习UI
        self.create_online_learning_ui()
        
        # 回测设置
        backtest_settings_frame = tk.Frame(self.backtest_frame, bg="white", padx=20, pady=10)
        backtest_settings_frame.pack(fill=tk.X)
        
        backtest_label = tk.Label(backtest_settings_frame, text="回测期数:", bg="white", font=("SimHei", 10))
        backtest_label.pack(side=tk.LEFT, padx=5)
        
        self.backtest_periods_var = tk.StringVar(value="10")
        backtest_entry = tk.Entry(backtest_settings_frame, textvariable=self.backtest_periods_var, width=10)
        backtest_entry.pack(side=tk.LEFT, padx=5)
        
        backtest_btn = tk.Button(backtest_settings_frame, text="单独回测", command=self.run_backtest, 
                                font=("SimHei", 10), bg="#FF5722", fg="white", relief=tk.RAISED, padx=10)
        backtest_btn.pack(side=tk.LEFT, padx=5)
        
        self.backtest_status_var = tk.StringVar()
        self.backtest_status_var.set("请先选择文件并开始回测")
        backtest_status_label = tk.Label(backtest_settings_frame, textvariable=self.backtest_status_var, 
                                        bg="white", font=("SimHei", 10))
        backtest_status_label.pack(side=tk.LEFT, padx=10)
        
        # 回测结果框架 - 添加滚动条支持
        self.backtest_result_frame = tk.Frame(self.backtest_frame, bg="white")
        self.backtest_result_frame.pack(fill=tk.BOTH, expand=True, padx=20, pady=10)
        
        # 创建Canvas和Scrollbar
        # 创建Canvas和滚动条（垂直+水平）
        self.backtest_canvas = tk.Canvas(self.backtest_result_frame, bg="white", highlightthickness=0)
        self.vscrollbar = tk.Scrollbar(self.backtest_result_frame, orient="vertical", command=self.backtest_canvas.yview)
        self.hscrollbar = tk.Scrollbar(self.backtest_result_frame, orient="horizontal", command=self.backtest_canvas.xview)
        self.backtest_scrollable_frame = tk.Frame(self.backtest_canvas, bg="white")

        # 配置Canvas滚动区域
        # 绑定滚动区域和框架宽度调整
        def on_frame_configure(_):
            self.backtest_canvas.configure(scrollregion=self.backtest_canvas.bbox("all"))
        self.backtest_scrollable_frame.bind("<Configure>", on_frame_configure)

        def on_canvas_configure(e):
            # 设置框架宽度以匹配Canvas
            self.backtest_canvas.itemconfig(frame_id, width=e.width)
        self.backtest_canvas.bind("<Configure>", on_canvas_configure)

        # 创建窗口并保存ID
        frame_id = self.backtest_canvas.create_window((0, 0), window=self.backtest_scrollable_frame, anchor="nw")
        self.backtest_canvas.configure(yscrollcommand=self.vscrollbar.set, xscrollcommand=self.hscrollbar.set)

        # 放置滚动条和Canvas
        self.vscrollbar.pack(side="right", fill="y")
        self.hscrollbar.pack(side="bottom", fill="x")
        self.backtest_canvas.pack(side="left", fill="both", expand=True)
        
        # 底部状态栏
        self.status_var = tk.StringVar()
        self.status_var.set("就绪")
        status_bar = tk.Label(self.root, textvariable=self.status_var, bd=1, relief=tk.SUNKEN, anchor=tk.W)
        status_bar.pack(side=tk.BOTTOM, fill=tk.X)

        # UI创建完成后，尝试自动初始化在线学习
        self.auto_initialize_online_learning()
    
    def browse_file(self):
        file_path = filedialog.askopenfilename(
            title="选择Excel文件",
            filetypes=[("Excel files", "*.xlsx *.xls")],
            initialfile="data.xlsx"  # 设置默认文件名
        )
        if file_path:
            self.file_path_var.set(file_path)
            # 保存文件路径到配置
            self.config['last_excel_file'] = file_path
            save_config(self.config)
            # 更新数据文件路径
            self.data_file = file_path
    
    def load_data(self, file_path):
        try:
            # 读取Excel文件
            df = pd.read_excel(file_path)
            
            # 验证数据格式
            required_columns = ['期号', '万位', '千位', '百位', '十位', '个位']
            if not all(col in df.columns for col in required_columns):
                raise ValueError("Excel文件缺少必要的列")
            
            # 获取最后一期期号
            last_period = df['期号'].iloc[-1]
            
            # 提取期号数据
            periods_data = df['期号'].values
            
            # 提取开奖数据
            data = df[['万位', '千位', '百位', '十位', '个位']].values
            
            # 验证数据范围
            if not np.all((data >= 0) & (data <= 9)):
                raise ValueError("开奖数据包含无效数字（应在0-9范围内）")
                
            return data, len(data), last_period, periods_data
        
        except Exception as e:
            messagebox.showerror("数据加载错误", str(e))
            return None, 0, None, None
    
    def run_backtest_and_predict(self):
        """执行回测并基于回测结果进行预测"""
        file_path = self.file_path_var.get()
        if file_path == "未选择文件":
            messagebox.showwarning("警告", "请先选择Excel文件")
            return
        
        try:
            backtest_periods = int(self.backtest_periods_var.get())
            if backtest_periods <= 0:
                messagebox.showwarning("警告", "回测期数必须大于0")
                return
        except ValueError:
            messagebox.showwarning("警告", "请输入有效的回测期数")
            return
            
        # 在新线程中运行回测和预测，避免界面卡顿
        self.status_var.set("正在加载数据...")
        self.root.update()
        
        threading.Thread(target=self._run_backtest_and_predict, args=(backtest_periods,)).start()
    
    def _run_backtest_and_predict(self, backtest_periods):
        try:
            # 加载数据
            data, num_periods, last_period, periods_data = self.load_data(self.file_path_var.get())
            if data is None:
                self.status_var.set("就绪")
                return
                
            self.history_data = data
            self.last_period = last_period  # 保存最后一期期号
            self.periods_data = periods_data  # 保存期号数据
            
            # 检查回测期数是否合理
            if backtest_periods >= len(data):
                backtest_periods = len(data) - 1
                self.backtest_periods_var.set(str(backtest_periods))
                messagebox.showinfo("提示", f"回测期数已自动调整为: {backtest_periods}")
            
            # 执行回测
            self.status_var.set("正在进行回测...")
            self.backtest_status_var.set("正在进行回测...")
            
            total_periods = len(self.history_data)
            hit_rates = []
            full_hit_count = 0
            
            # 定义两两组合
            position_pairs = [
                ('万位', '千位', 0, 1), ('万位', '百位', 0, 2), ('万位', '十位', 0, 3),
                ('千位', '百位', 1, 2), ('千位', '十位', 1, 3),
                ('百位', '十位', 2, 3)
            ]
            
            # 初始化两两组合统计
            pair_stats = {}
            for pair_name1, pair_name2, pos1, pos2 in position_pairs:
                pair_key = f"{pair_name1[0]}{pair_name2[0]}"
                pair_stats[pair_key] = {'hit_count': 0, 'total_count': 0}
            
            backtest_results = []
            
            # 使用当前参数初始化模型
            current_model = MFTNModel(self.get_current_params())
            
            # 对每一期进行回测
            for i in range(total_periods - backtest_periods, total_periods):
                # 获取当前期号
                current_period = self.periods_data[i]
                
                # 获取实际开奖号码
                actual_numbers = self.history_data[i]
                
                # 准备训练数据（不包括当前期）
                train_data = self.history_data[:i]
                
                # 训练模型
                current_model.fit(train_data)
                
                # 预测
                predictions = current_model.predict_next()
                
                # 计算命中情况
                position_hits = []
                for pos in range(4):  # 只比较前4位
                    predicted_nums = predictions[pos]
                    actual_num = actual_numbers[pos]
                    hit = actual_num in predicted_nums
                    position_hits.append(hit)
                
                # 计算两两组合的命中情况
                pair_hits = {}
                for pair_name1, pair_name2, pos1, pos2 in position_pairs:
                    if pos1 < 4 and pos2 < 4:  # 只考虑前4位的组合
                        pair_key = f"{pair_name1[0]}{pair_name2[0]}"
                        hit1 = actual_numbers[pos1] in predictions[pos1]
                        hit2 = actual_numbers[pos2] in predictions[pos2]
                        pair_hit = hit1 and hit2
                        pair_hits[pair_key] = pair_hit
                        
                        # 更新统计
                        pair_stats[pair_key]['total_count'] += 1
                        if pair_hit:
                            pair_stats[pair_key]['hit_count'] += 1
                
                # 计算当前期的命中率
                hit_rate = sum(position_hits) / 4.0
                hit_rates.append(hit_rate)
                
                # 检查是否4位全中
                if all(position_hits):
                    full_hit_count += 1
                
                # 保存回测结果
                backtest_results.append({
                    'period': current_period,
                    'actual': actual_numbers,
                    'predictions': predictions,
                    'position_hits': position_hits,
                    'pair_hits': pair_hits,
                    'hit_rate': hit_rate
                })
                
                # 更新状态
                progress = (i - (total_periods - backtest_periods) + 1) / backtest_periods * 100
                self.root.after(0, self.backtest_status_var.set, 
                               f"正在进行回测: {i - (total_periods - backtest_periods) + 1}/{backtest_periods} ({progress:.1f}%)")
                self.root.update_idletasks()  # 强制更新UI
            
            # 计算总体命中率和4位全中率
            overall_hit_rate = sum(hit_rates) / len(hit_rates)
            full_hit_rate = full_hit_count / len(hit_rates)
            
            # 更新UI显示回测结果
            self.root.after(0, self._update_backtest_display, backtest_results, overall_hit_rate, full_hit_rate, pair_stats)
            
            # 使用全部数据训练模型并进行预测
            self.status_var.set("正在训练模型...")
            self.root.update()
            
            # 使用当前参数初始化模型
            self.model = MFTNModel(self.get_current_params())
            
            # 训练模型
            self.model.fit(self.history_data)
            
            self.status_var.set("正在预测...")
            self.root.update()
            
            # 预测
            self.predictions = self.model.predict_next()
            
            # 更新UI显示预测结果
            self.root.after(0, self._update_result_display, num_periods)
            
            # 显示回测结果摘要
            pair_summary = "\n".join([f"{k}: {v['hit_count']}/{v['total_count']} ({v['hit_count']/v['total_count']*100:.1f}%)" 
                                    for k, v in pair_stats.items()])
            self.root.after(0, messagebox.showinfo, "回测结果", 
                           f"回测完成!\n回测期数: {len(backtest_results)}\n平均命中率: {overall_hit_rate*100:.2f}%\n4位全中率: {full_hit_rate*100:.2f}%\n\n两两组合命中率:\n{pair_summary}")
            
        except Exception as e:
            self.root.after(0, messagebox.showerror, "错误", f"回测或预测过程中发生错误: {str(e)}")
        finally:
            self.root.after(0, self.status_var.set, "就绪")
            self.root.after(0, self.backtest_status_var.set, "回测完成")
    
    def run_backtest(self):
        """仅执行回测，不进行预测"""
        if self.history_data is None:
            messagebox.showwarning("警告", "请先加载数据")
            return
            
        try:
            backtest_periods = int(self.backtest_periods_var.get())
            if backtest_periods <= 0 or backtest_periods >= len(self.history_data):
                messagebox.showwarning("警告", f"回测期数必须在1到{len(self.history_data)-1}之间")
                return
        except ValueError:
            messagebox.showwarning("警告", "请输入有效的回测期数")
            return
            
        # 在新线程中运行回测，避免界面卡顿
        self.backtest_status_var.set("正在进行回测...")
        threading.Thread(target=self._run_backtest, args=(backtest_periods,)).start()
    
    def _run_backtest(self, backtest_periods):
        try:
            # 直接使用全部数据的最后N期进行回测
            total_periods = len(self.history_data)

            # 确保回测期数不超过数据总量
            if backtest_periods >= total_periods:
                backtest_periods = total_periods - 10  # 至少保留10期作为训练数据

            print(f"🎯 实际回测：直接使用全部数据最后{backtest_periods}期")
            print(f"   数据总量: {total_periods}期")
            print(f"   回测期数: {backtest_periods}期")
            print(f"   回测范围: 第{total_periods-backtest_periods+1}-{total_periods}期")

            hit_rates = []
            full_hit_count = 0
            
            # 定义两两组合
            position_pairs = [
                ('万位', '千位', 0, 1), ('万位', '百位', 0, 2), ('万位', '十位', 0, 3),
                ('千位', '百位', 1, 2), ('千位', '十位', 1, 3),
                ('百位', '十位', 2, 3)
            ]
            
            # 初始化两两组合统计
            pair_stats = {}
            for pair_name1, pair_name2, pos1, pos2 in position_pairs:
                pair_key = f"{pair_name1[0]}{pair_name2[0]}"
                pair_stats[pair_key] = {'hit_count': 0, 'total_count': 0}
            
            backtest_results = []
            
            # 使用当前参数初始化模型
            current_model = MFTNModel(self.get_current_params())
            
            # 对每一期进行回测
            for i in range(total_periods - backtest_periods, total_periods):
                # 获取当前期号
                current_period = self.periods_data[i]
                
                # 获取实际开奖号码
                actual_numbers = self.history_data[i]
                
                # 准备训练数据（不包括当前期）
                train_data = self.history_data[:i]
                
                # 训练模型
                current_model.fit(train_data)
                
                # 预测
                predictions = current_model.predict_next()
                
                # 计算命中情况
                position_hits = []
                for pos in range(4):  # 只比较前4位
                    predicted_nums = predictions[pos]
                    actual_num = actual_numbers[pos]
                    hit = actual_num in predicted_nums
                    position_hits.append(hit)
                
                # 计算两两组合的命中情况
                pair_hits = {}
                for pair_name1, pair_name2, pos1, pos2 in position_pairs:
                    if pos1 < 4 and pos2 < 4:  # 只考虑前4位的组合
                        pair_key = f"{pair_name1[0]}{pair_name2[0]}"
                        hit1 = actual_numbers[pos1] in predictions[pos1]
                        hit2 = actual_numbers[pos2] in predictions[pos2]
                        pair_hit = hit1 and hit2
                        pair_hits[pair_key] = pair_hit
                        
                        # 更新统计
                        pair_stats[pair_key]['total_count'] += 1
                        if pair_hit:
                            pair_stats[pair_key]['hit_count'] += 1
                
                # 计算当前期的命中率
                hit_rate = sum(position_hits) / 4.0
                hit_rates.append(hit_rate)
                
                # 检查是否4位全中
                if all(position_hits):
                    full_hit_count += 1
                
                # 保存回测结果
                backtest_results.append({
                    'period': current_period,
                    'actual': actual_numbers,
                    'predictions': predictions,
                    'position_hits': position_hits,
                    'pair_hits': pair_hits,
                    'hit_rate': hit_rate
                })
                
                # 更新状态
                progress = (i - (total_periods - backtest_periods) + 1) / backtest_periods * 100
                self.root.after(0, self.backtest_status_var.set, 
                               f"正在进行回测: {i - (total_periods - backtest_periods) + 1}/{backtest_periods} ({progress:.1f}%)")
            
            # 计算总体命中率和4位全中率
            overall_hit_rate = sum(hit_rates) / len(hit_rates)
            full_hit_rate = full_hit_count / len(hit_rates)
            
            # 更新UI显示回测结果
            self.root.after(0, self._update_backtest_display, backtest_results, overall_hit_rate, full_hit_rate, pair_stats)
            
            # 显示回测结果摘要
            pair_summary = "\n".join([f"{k}: {v['hit_count']}/{v['total_count']} ({v['hit_count']/v['total_count']*100:.1f}%)" 
                                    for k, v in pair_stats.items()])
            self.root.after(0, messagebox.showinfo, "回测结果", 
                           f"回测完成!\n回测期数: {len(backtest_results)}\n平均命中率: {overall_hit_rate*100:.2f}%\n4位全中率: {full_hit_rate*100:.2f}%\n\n两两组合命中率:\n{pair_summary}")
            
        except Exception as e:
            self.root.after(0, messagebox.showerror, "错误", f"回测过程中发生错误: {str(e)}")
        finally:
            self.root.after(0, self.backtest_status_var.set, "回测完成")
    
    def _update_backtest_display(self, results, overall_hit_rate, full_hit_rate, pair_stats):
        # 清除现有结果
        for widget in self.backtest_scrollable_frame.winfo_children():
            widget.destroy()
        
        # 显示当前使用的参数
        params_text = "回测参数: " + ", ".join([f"{k}={v}" for k, v in self.get_current_params().items()])
        params_label = tk.Label(self.backtest_scrollable_frame, text=params_text, font=("SimHei", 10), bg="white", pady=5)
        params_label.pack(fill=tk.X)
        
        # 显示回测统计信息
        stats_frame = tk.Frame(self.backtest_scrollable_frame, bg="white")
        stats_frame.pack(fill=tk.X, pady=10)
        
        tk.Label(stats_frame, text=f"回测期数: {len(results)}", font=("SimHei", 12, "bold"), bg="white").pack(side=tk.LEFT, padx=20)
        tk.Label(stats_frame, text=f"平均命中率: {overall_hit_rate*100:.2f}%", font=("SimHei", 12, "bold"), bg="white").pack(side=tk.LEFT, padx=20)
        tk.Label(stats_frame, text=f"4位全中率: {full_hit_rate*100:.2f}%", font=("SimHei", 12, "bold"), bg="white", fg="#FF5722").pack(side=tk.LEFT, padx=20)
        
        # 添加两两组合统计表格
        pair_stats_frame = tk.Frame(self.backtest_scrollable_frame, bg="white")
        pair_stats_frame.pack(fill=tk.BOTH, expand=True, pady=10)

        tk.Label(pair_stats_frame, text="两两组合命中率统计", font=("SimHei", 14, "bold"), bg="white", fg="#2196F3").pack(pady=5)

        # 创建两两组合统计表格
        pair_table_frame = tk.Frame(pair_stats_frame, bg="white")
        pair_table_frame.pack(fill=tk.BOTH, expand=True, padx=20)
        
        # 表头
        pair_headers = ["组合", "命中次数", "总次数", "命中率", "状态"]
        for col, header in enumerate(pair_headers):
            tk.Label(pair_table_frame, text=header, font=("SimHei", 10, "bold"),
                    bg="#e0e0e0", relief=tk.RAISED, bd=1, width=12).grid(row=0, column=col, sticky="nsew")
            pair_table_frame.columnconfigure(col, weight=1)
        
        # 组合名称映射
        pair_name_map = {
            '万千': '万位-千位', '万百': '万位-百位', '万十': '万位-十位',
            '千百': '千位-百位', '千十': '千位-十位',
            '百十': '百位-十位'
        }
        
        # 填充两两组合数据
        row = 1
        for pair_key, stats in pair_stats.items():
            hit_count = stats['hit_count']
            total_count = stats['total_count']
            hit_rate = hit_count / total_count if total_count > 0 else 0
            
            # 组合名称
            display_name = pair_name_map.get(pair_key, pair_key)
            tk.Label(pair_table_frame, text=display_name, font=("SimHei", 10),
                    bg="white", relief=tk.SUNKEN, bd=1).grid(row=row, column=0, sticky="nsew")
            
            # 命中次数
            tk.Label(pair_table_frame, text=str(hit_count), font=("SimHei", 10),
                    bg="white", relief=tk.SUNKEN, bd=1).grid(row=row, column=1, sticky="nsew")
            
            # 总次数
            tk.Label(pair_table_frame, text=str(total_count), font=("SimHei", 10),
                    bg="white", relief=tk.SUNKEN, bd=1).grid(row=row, column=2, sticky="nsew")
            
            # 命中率
            rate_text = f"{hit_rate*100:.1f}%"
            rate_color = "#90EE90" if hit_rate >= 0.7 else "#FFE082" if hit_rate >= 0.5 else "#FFCCCC"
            tk.Label(pair_table_frame, text=rate_text, font=("SimHei", 10),
                    bg=rate_color, relief=tk.SUNKEN, bd=1).grid(row=row, column=3, sticky="nsew")
            
            # 状态
            if hit_rate >= 0.7:
                status = "优秀"
                status_color = "#4CAF50"
            elif hit_rate >= 0.5:
                status = "良好"
                status_color = "#FF9800"
            else:
                status = "一般"
                status_color = "#f44336"
            
            tk.Label(pair_table_frame, text=status, font=("SimHei", 10),
                    bg="white", fg=status_color, relief=tk.SUNKEN, bd=1).grid(row=row, column=4, sticky="nsew")
            
            row += 1
        
        # 设置列权重
        for col in range(5):
            pair_table_frame.grid_columnconfigure(col, weight=1)
        
        # 添加分隔线
        separator = tk.Frame(self.backtest_scrollable_frame, height=2, bg="#cccccc")
        separator.pack(fill=tk.X, pady=10)
        
        # 创建详细结果表格
        detail_label = tk.Label(self.backtest_scrollable_frame, text="详细回测结果", font=("SimHei", 14, "bold"), bg="white", fg="#2196F3")
        detail_label.pack(pady=5)
        
        table_frame = tk.Frame(self.backtest_scrollable_frame, bg="white")
        table_frame.pack(fill=tk.BOTH, expand=True, pady=10)
        
        # 创建表头
        headers = ["期号", "实际开奖", "万位预测", "千位预测", "百位预测", "十位预测", "命中率", "4位全中", "万千", "万百", "万十", "千百", "千十", "百十"]
        column_widths = [6, 10, 9, 9, 9, 9, 8, 8, 6, 6, 6, 6, 6, 6]  # 调整每列宽度
        for col, (name, width) in enumerate(zip(headers, column_widths)):
            header = tk.Label(table_frame, text=name, font=('SimHei', 10, 'bold'),
                             bg='#e0e0e0', relief=tk.RAISED, bd=1, width=width)
            header.grid(row=0, column=col, sticky='nsew', padx=1, pady=1)
        
        # 设置列最小宽度而非权重
        for col, width in enumerate(column_widths):
            table_frame.grid_columnconfigure(col, minsize=width*10)  # 每个字符约10像素
        
        # 填充表格数据
        for row, result in enumerate(results):
            # 期号
            tk.Label(table_frame, text=str(result['period']), font=("SimHei", 9),
                    bg="white", relief=tk.SUNKEN, bd=1).grid(row=row+1, column=0, sticky="nsew")
            
            # 实际开奖
            actual_text = " ".join(map(str, result['actual']))
            tk.Label(table_frame, text=actual_text, font=("SimHei", 9),
                    bg="white", relief=tk.SUNKEN, bd=1).grid(row=row+1, column=1, sticky="nsew")
            
            # 各位置预测
            for pos in range(4):  # 只显示前4位
                predicted_nums = sorted(result['predictions'][pos])
                predicted_text = ", ".join(map(str, predicted_nums))
                bg_color = "#90EE90" if result['position_hits'][pos] else "#FFCCCC"
                tk.Label(table_frame, text=predicted_text, font=("SimHei", 8),
                        bg=bg_color, relief=tk.SUNKEN, bd=1).grid(row=row+1, column=pos+2, sticky="nsew")
            
            # 命中率
            hit_rate_text = f"{result['hit_rate']*100:.1f}%"
            tk.Label(table_frame, text=hit_rate_text, font=("SimHei", 9),
                    bg="white", relief=tk.SUNKEN, bd=1).grid(row=row+1, column=6, sticky="nsew")
            
            # 4位全中
            full_hit_text = "是" if all(result['position_hits']) else "否"
            bg_color = "#90EE90" if all(result['position_hits']) else "#FFCCCC"
            tk.Label(table_frame, text=full_hit_text, font=("SimHei", 9),
                    bg=bg_color, relief=tk.SUNKEN, bd=1).grid(row=row+1, column=7, sticky="nsew")
            
            # 两两组合命中情况
            pair_keys = ['万千', '万百', '万十', '千百', '千十', '百十']
            for col_idx, pair_key in enumerate(pair_keys):
                if pair_key in result['pair_hits']:
                    hit_status = "✓" if result['pair_hits'][pair_key] else "✗"
                    bg_color = "#90EE90" if result['pair_hits'][pair_key] else "#FFCCCC"
                else:
                    hit_status = "-"
                    bg_color = "white"
                
                tk.Label(table_frame, text=hit_status, font=("SimHei", 10),
                        bg=bg_color, relief=tk.SUNKEN, bd=1).grid(row=row+1, column=8+col_idx, sticky="nsew")
        
        # 更新Canvas的滚动区域
        self.backtest_canvas.configure(scrollregion=self.backtest_canvas.bbox("all"))
        
        # 切换到回测标签页
        self.notebook.select(2)
    
    def _update_result_display(self, num_periods):
        # 清除现有结果
        for widget in self.result_frame.winfo_children():
            widget.destroy()
        
        # 显示当前使用的参数
        params_text = "当前参数: " + ", ".join([f"{k}={v}" for k, v in self.get_current_params().items()])
        params_label = tk.Label(self.result_frame, text=params_text, font=("SimHei", 10), bg="white", pady=5)
        params_label.pack(fill=tk.X)
        
        # 计算预测期号
        if self.last_period is not None:
            # 尝试解析期号格式
            try:
                if isinstance(self.last_period, str):
                    # 处理类似 "2023001" 或 "第2023001期" 格式
                    import re
                    match = re.search(r'\d+', self.last_period)
                    if match:
                        number_part = match.group(0)
                        prefix = self.last_period[:self.last_period.index(number_part)]
                        suffix = self.last_period[self.last_period.index(number_part)+len(number_part):]
                        next_number = int(number_part) + 1
                        next_period = f"{prefix}{next_number}{suffix}"
                    else:
                        # 无法解析，直接加1
                        next_period = str(int(self.last_period) + 1)
                else:
                    next_period = self.last_period + 1
            except:
                next_period = f"{self.last_period + 1}"
            
            # 显示预测期号
            period_label = tk.Label(self.result_frame, text=f"预测期号: {next_period}",
                                  font=("SimHei", 14, "bold"), bg="white", pady=10, fg="#FF5722")
            period_label.pack()
        
        # 显示基本信息
        info_label = tk.Label(self.result_frame, text=f"已分析 {num_periods} 期历史数据",
                             font=("SimHei", 12, "bold"), bg="white", pady=10)
        info_label.pack()
        
        # 创建结果表格
        position_names = ['万位', '千位', '百位', '十位', '个位']
        
        # 创建表格框架
        table_frame = tk.Frame(self.result_frame, bg="white")
        table_frame.pack(fill=tk.BOTH, expand=True, padx=20, pady=10)
        
        # 创建表头
        for col, name in enumerate(["位置", "推荐数字", "热度指数", "遗漏期数"]):
            header = tk.Label(table_frame, text=name, font=("SimHei", 10, "bold"),
                             bg="#e0e0e0", relief=tk.RAISED, bd=1, width=20)
            header.grid(row=0, column=col, sticky="nsew")
        
        # 设置列权重，使其均匀分布
        for col in range(4):
            table_frame.grid_columnconfigure(col, weight=1)
        
        # 填充表格数据
        for row, pos in enumerate(range(5)):
            # 位置名称
            pos_label = tk.Label(table_frame, text=position_names[pos], font=("SimHei", 10),
                                bg="white", relief=tk.SUNKEN, bd=1)
            pos_label.grid(row=row+1, column=0, sticky="nsew")
            
            # 推荐数字
            nums = sorted(self.predictions[pos])
            nums_label = tk.Label(table_frame, text=", ".join(map(str, nums)), font=("SimHei", 10),
                                 bg="white", relief=tk.SUNKEN, bd=1)
            nums_label.grid(row=row+1, column=1, sticky="nsew")
            
            # 热度指数
            heat_data = self.model.heat_index[pos]
            heat_str = ", ".join([f"{i}:{heat_data[i]:.2f}" for i in nums])
            heat_label = tk.Label(table_frame, text=heat_str, font=("SimHei", 10),
                                 bg="white", relief=tk.SUNKEN, bd=1)
            heat_label.grid(row=row+1, column=2, sticky="nsew")
            
            # 遗漏期数
            miss_data = self.model.miss_counts[pos]
            miss_str = ", ".join([f"{i}:{int(miss_data[i])}" for i in nums])
            miss_label = tk.Label(table_frame, text=miss_str, font=("SimHei", 10),
                                 bg="white", relief=tk.SUNKEN, bd=1)
            miss_label.grid(row=row+1, column=3, sticky="nsew")
        
        # 添加历史统计图表
        self._create_statistics_charts()
        
        # 切换到结果标签页
        self.notebook.select(0)
    
    def _create_statistics_charts(self):
        # 清除现有图表
        for widget in self.stats_frame.winfo_children():
            widget.destroy()
        
        if self.history_data is None:
            return
            
        position_names = ['万位', '千位', '百位', '十位', '个位']
        
        # 创建图表框架
        chart_frame = tk.Frame(self.stats_frame, bg="white")
        chart_frame.pack(fill=tk.BOTH, expand=True, padx=20, pady=10)
        
        # 创建一个图表
        fig, axes = plt.subplots(5, 1, figsize=(10, 15))
        fig.subplots_adjust(hspace=0.5)
        
        # 为每个位置创建频率分布图
        for pos in range(5):
            # 计算数字频率
            counts = np.bincount(self.history_data[:, pos], minlength=10)
            frequencies = counts / len(self.history_data)
            
            # 创建柱状图
            axes[pos].bar(range(10), frequencies, color='skyblue')
            axes[pos].set_title(f'{position_names[pos]}数字频率分布')
            axes[pos].set_xlabel('数字')
            axes[pos].set_ylabel('频率')
            axes[pos].set_xticks(range(10))
            axes[pos].grid(axis='y', linestyle='--', alpha=0.7)
            
            # 添加数值标签
            for i, v in enumerate(frequencies):
                axes[pos].text(i, v + 0.002, f'{v:.3f}', ha='center')
        
        # 将图表嵌入到Tkinter窗口
        canvas = FigureCanvasTkAgg(fig, master=chart_frame)
        canvas.draw()
        canvas.get_tk_widget().pack(fill=tk.BOTH, expand=True)
    
    def show_params_dialog(self):
        """显示参数设置对话框"""
        dialog = tk.Toplevel(self.root)
        dialog.title("模型参数设置")
        dialog.geometry("500x600")
        dialog.resizable(False, False)
        dialog.transient(self.root)
        dialog.grab_set()
        
        # 创建参数输入框
        param_entries = {}
        param_labels = {
            'alpha': '平滑因子 (alpha):',
            'lambda': '冷号衰减系数 (lambda):',
            'short_weight': '短期预测权重:',
            'long_weight': '长期预测权重:',
            'mid_weight': '中期预测权重:',
            'co_weight': '协同预测权重:',
            'hot_threshold': '热号阈值:',
            'cold_threshold': '冷号阈值:',
            'selection_count': '选择数量:',
            'window': '中期窗口大小:',
            'periodicity': '周期特征期数:',
            'hot_multiplier': '热号权重系数:',
            'cold_multiplier': '冷号权重系数:'
        }
        
        # 获取当前参数值
        current_params = self.get_current_params()
        
        frame = tk.Frame(dialog, padx=20, pady=10)
        frame.pack(fill=tk.BOTH, expand=True)
        
        for i, (param, label_text) in enumerate(param_labels.items()):
            tk.Label(frame, text=label_text, font=("SimHei", 10)).grid(row=i, column=0, sticky=tk.W, pady=5)
            
            if param in ['selection_count', 'window', 'periodicity']:
                var = tk.IntVar(value=int(current_params[param]))
            else:
                var = tk.DoubleVar(value=current_params[param])
            entry = tk.Entry(frame, textvariable=var, width=10)
            entry.grid(row=i, column=1, sticky=tk.W, pady=5)
            
            param_entries[param] = var
        
        # 添加说明文本
        help_text = "提示:\n" \
                   "- 短期、中期、长期和协同预测权重之和应为1\n" \
                   "- 调整参数后需重新回测和预测\n" \
                   "- 参数将自动保存到配置文件"
        tk.Label(frame, text=help_text, font=("SimHei", 9), fg="blue", justify=tk.LEFT).grid(
            row=len(param_labels), column=0, columnspan=2, sticky=tk.W, pady=10)
        
        # 按钮框架
        btn_frame = tk.Frame(dialog)
        btn_frame.pack(fill=tk.X, padx=20, pady=10)
        
        # 确定按钮
        def save_params():
            new_params = {}
            for param, var in param_entries.items():
                try:
                    if param == 'selection_count':
                        value = int(var.get())
                    else:
                        value = float(var.get())
                    new_params[param] = value
                except ValueError:
                    messagebox.showerror("参数错误", f"{param_labels[param]}必须是数字")
                    return

            # 验证权重之和是否接近1
            weights = [new_params['short_weight'], new_params['long_weight'], new_params['mid_weight'], new_params['co_weight']]
            if abs(sum(weights) - 1.0) > 0.01:
                messagebox.showwarning("权重警告", "四种预测方法的权重之和应接近1.0")

            # 保存参数到配置文件（保持last_excel_file不变）
            self.config.update(new_params)
            if save_config(self.config):
                # 更新当前参数
                self.default_params = new_params
                # 清除优化参数，让用户手动设置的参数优先生效
                self.best_optimized_params = None
                messagebox.showinfo("参数设置", "参数已保存到配置文件，下次预测将使用新参数")
                dialog.destroy()
            else:
                messagebox.showerror("保存失败", "参数保存到配置文件失败")
        
        tk.Button(btn_frame, text="确定", command=save_params, bg="#4CAF50", fg="white", 
                 padx=15).pack(side=tk.LEFT, padx=5)
        
        # 重置按钮
        def reset_params():
            for param, var in param_entries.items():
                var.set(DEFAULT_CONFIG[param])
        
        tk.Button(btn_frame, text="重置默认", command=reset_params, bg="#FF9800", fg="white", 
                 padx=15).pack(side=tk.LEFT, padx=5)
        
        tk.Button(btn_frame, text="取消", command=dialog.destroy, bg="#f44336", fg="white", 
                 padx=15).pack(side=tk.LEFT, padx=5)
    
    def get_current_params(self):
        """获取当前使用的参数"""
        # 优先使用用户手动设置的参数，如果没有则使用优化后的最佳参数
        if hasattr(self, 'best_optimized_params') and self.best_optimized_params:
            print("DEBUG: 使用优化后的最佳参数进行回测")
            return self.best_optimized_params.copy()
        else:
            print("DEBUG: 使用默认参数进行回测")
            return self.default_params.copy()


    
    def auto_optimize_params(self):
        """自动优化参数"""
        if self.history_data is None:
            messagebox.showwarning("警告", "请先加载数据")
            return
            
        # 创建参数优化对话框
        dialog = tk.Toplevel(self.root)
        dialog.title("参数自动优化")
        dialog.geometry("1100x700")  # 修改为更大的窗口：宽1000，高700
        dialog.resizable(True, True)   # 允许用户调整窗口大小
        dialog.transient(self.root)
        dialog.grab_set()
        
        # 创建主框架
        main_frame = tk.Frame(dialog)
        main_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=5)
        
        # 创建左右分栏
        left_frame = tk.Frame(main_frame, relief=tk.GROOVE, bd=2)
        left_frame.pack(side=tk.LEFT, fill=tk.BOTH, expand=True, padx=5, pady=5)
        
        right_frame = tk.Frame(main_frame, relief=tk.GROOVE, bd=2)
        right_frame.pack(side=tk.LEFT, fill=tk.BOTH, expand=True, padx=5, pady=5)
        
        # 左侧：参数设置
        title_frame = tk.Frame(left_frame, bg="#4CAF50")
        title_frame.pack(fill=tk.X)
        tk.Label(title_frame, text="优化设置", font=("SimHei", 12, "bold"), 
                bg="#4CAF50", fg="white", pady=5).pack()
        
        # 参数设置区域
        params_frame = tk.Frame(left_frame, padx=20, pady=10)
        params_frame.pack(fill=tk.BOTH)
        
        # 回测期数
        param_frame1 = tk.Frame(params_frame)
        param_frame1.pack(fill=tk.X, pady=5)
        tk.Label(param_frame1, text="回测期数:", font=("SimHei", 10)).pack(side=tk.LEFT)
        backtest_periods_var = tk.IntVar(value=30)
        backtest_periods_entry = tk.Entry(param_frame1, textvariable=backtest_periods_var, width=10)
        backtest_periods_entry.pack(side=tk.LEFT, padx=5)
        
        # 目标命中率
        param_frame2 = tk.Frame(params_frame)
        param_frame2.pack(fill=tk.X, pady=5)
        tk.Label(param_frame2, text="目标命中率(%):", font=("SimHei", 10)).pack(side=tk.LEFT)
        target_hit_rate_var = tk.DoubleVar(value=85.0)
        target_hit_rate_entry = tk.Entry(param_frame2, textvariable=target_hit_rate_var, width=10)
        target_hit_rate_entry.pack(side=tk.LEFT, padx=5)
        
        # 最大迭代次数
        param_frame3 = tk.Frame(params_frame)
        param_frame3.pack(fill=tk.X, pady=5)
        tk.Label(param_frame3, text="最大迭代次数:", font=("SimHei", 10)).pack(side=tk.LEFT)
        max_iterations_var = tk.IntVar(value=1000)
        max_iterations_entry = tk.Entry(param_frame3, textvariable=max_iterations_var, width=10)
        max_iterations_entry.pack(side=tk.LEFT, padx=5)
        
        # 种群大小
        param_frame4 = tk.Frame(params_frame)
        param_frame4.pack(fill=tk.X, pady=5)
        tk.Label(param_frame4, text="种群大小:", font=("SimHei", 10)).pack(side=tk.LEFT)
        population_size_var = tk.IntVar(value=50)
        population_size_entry = tk.Entry(param_frame4, textvariable=population_size_var, width=10)
        population_size_entry.pack(side=tk.LEFT, padx=5)
        
        # 线程数
        param_frame5 = tk.Frame(params_frame)
        param_frame5.pack(fill=tk.X, pady=5)
        tk.Label(param_frame5, text="线程数:", font=("SimHei", 10)).pack(side=tk.LEFT)
        num_threads_var = tk.IntVar(value=4)
        num_threads_entry = tk.Entry(param_frame5, textvariable=num_threads_var, width=10)
        num_threads_entry.pack(side=tk.LEFT, padx=5)
        
        # 进度显示框架
        progress_frame = tk.Frame(left_frame, padx=20, pady=10)
        progress_frame.pack(fill=tk.X)
        
        # 进度条
        progress_var = tk.DoubleVar()
        progress_bar = ttk.Progressbar(progress_frame, variable=progress_var, maximum=100, mode='determinate')
        progress_bar.pack(fill=tk.X, pady=5)
        
        # 当前迭代信息
        iteration_var = tk.StringVar(value="准备优化...")
        iteration_label = tk.Label(progress_frame, textvariable=iteration_var, font=("SimHei", 10))
        iteration_label.pack(pady=5)
        
        # 当前最佳结果
        best_result_var = tk.StringVar(value="当前最佳命中率: 0.00%")
        best_result_label = tk.Label(progress_frame, textvariable=best_result_var, font=("SimHei", 10))
        best_result_label.pack(pady=5)

        # 验证集命中率
        validation_result_var = tk.StringVar(value="验证集命中率: --")
        validation_result_label = tk.Label(progress_frame, textvariable=validation_result_var,
                                         font=("SimHei", 10), fg="#FF9800")
        validation_result_label.pack(pady=2)

        # 测试集4位全中命中率
        test_result_var = tk.StringVar(value="测试集4位全中命中率: --")
        test_result_label = tk.Label(progress_frame, textvariable=test_result_var,
                                   font=("SimHei", 10), fg="#4CAF50", relief=tk.RIDGE, padx=5)
        test_result_label.pack(pady=2)
        
        # 右侧：优化日志
        title_frame2 = tk.Frame(right_frame, bg="#2196F3")
        title_frame2.pack(fill=tk.X)
        tk.Label(title_frame2, text="优化日志", font=("SimHei", 12, "bold"), 
                bg="#2196F3", fg="white", pady=5).pack()
        
        # 创建日志文本框和滚动条
        log_frame = tk.Frame(right_frame, padx=10, pady=10)
        log_frame.pack(fill=tk.BOTH, expand=True)
        
        log_scrollbar = tk.Scrollbar(log_frame)
        log_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        
        log_text = tk.Text(log_frame, height=20, width=40, font=("SimHei", 9),
                          yscrollcommand=log_scrollbar.set, wrap=tk.WORD,
                          bg="#f5f5f5", relief=tk.SUNKEN)
        log_text.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        log_scrollbar.config(command=log_text.yview)
        
        # 添加日志函数
        def add_log(message):
            dialog.after(0, lambda: log_text.insert(tk.END, f"{message}\n"))
            dialog.after(0, lambda: log_text.see(tk.END))
            dialog.after(0, log_text.update)
        
        # 按钮框架
        btn_frame = tk.Frame(left_frame)
        btn_frame.pack(fill=tk.X, padx=20, pady=10)
        
        # 优化状态变量
        optimization_running = False
        current_thread = None
        current_optimizer = None
        
        def stop_optimization():
            """停止优化进程"""
            nonlocal optimization_running, current_thread, current_optimizer
            optimization_running = False

            # 停止优化器的计算
            if current_optimizer:
                print("DEBUG: 调用优化器停止方法")
                current_optimizer.stop_optimization()
                add_log("已发送停止信号给优化器")

            # 强制停止线程
            if current_thread and current_thread.is_alive():
                print("DEBUG: 等待优化线程结束...")
                add_log("等待优化线程结束...")
                current_thread.join(timeout=3)  # 等待3秒

                if current_thread.is_alive():
                    print("WARNING: 优化线程未能在3秒内结束")
                    add_log("⚠️ 优化线程未能正常结束，可能仍在后台运行")
                    add_log("建议重启程序以确保完全停止")
                else:
                    print("DEBUG: 优化线程已成功结束")
                    add_log("✅ 优化线程已成功停止")

            add_log("正在停止优化...")
            stop_button.config(state='disabled')
            start_button.config(state='normal')
            # 检查是否有可应用的最佳参数
            has_best_params = False
            best_params_source = ""

            # 1. 首先检查严格验证的完整结果
            if hasattr(apply_best_params, 'optimization_results') and apply_best_params.optimization_results:
                has_best_params = True
                best_params_source = "严格验证完整结果"
            # 2. 检查优化器中的最佳参数（可能是部分结果）
            elif current_optimizer and hasattr(current_optimizer, 'best_params') and current_optimizer.best_params:
                has_best_params = True
                best_params_source = "优化器部分结果"
                # 将优化器的结果转换为标准格式，以便应用按钮使用
                apply_best_params.optimization_results = {
                    'best_params': current_optimizer.best_params,
                    'test_score': getattr(current_optimizer, 'best_hit_rate', 0.0),
                    'source': 'partial_optimization'
                }
                add_log(f"从优化器获取到部分结果，命中率: {current_optimizer.best_hit_rate:.3f}")

            print(f"DEBUG: 检查最佳参数结果 - 有参数: {has_best_params}, 来源: {best_params_source}")

            if has_best_params:
                # 应用启用样式
                apply_button.config(**apply_button.enabled_style)
                add_log(f"启用应用按钮 - 参数来源: {best_params_source}")
                print("DEBUG: 停止优化时启用了应用按钮")

                # 评估测试集命中率（手动停止时）
                try:
                    if current_optimizer and hasattr(current_optimizer, 'best_params') and current_optimizer.best_params:
                        add_log("🎯 手动停止优化，评估测试集命中率...")

                        # 从进度回调函数获取测试数据（如果可用）
                        if hasattr(progress_callback, 'test_data') and progress_callback.test_data is not None:
                            test_data = progress_callback.test_data
                        else:
                            # 备用方案：重新计算测试数据
                            if self.history_data is not None:
                                total_periods = len(self.history_data)
                                train_size = int(total_periods * 0.7)
                                validation_size = int(total_periods * 0.15)
                                test_data = self.history_data[train_size + validation_size:]
                            else:
                                add_log("无法获取历史数据")
                                test_data = None

                        if test_data is not None and len(test_data) > 30:  # 确保测试数据足够
                            # 尝试从进度回调获取用户设置的回测期数
                            user_periods = getattr(progress_callback, 'user_backtest_periods', 20)
                            max_allowed = len(test_data) - 30
                            actual_periods = min(user_periods, max_allowed)

                            print(f"DEBUG: 手动停止评估使用回测期数: {actual_periods}期")

                            test_score = current_optimizer._evaluate_params_on_data(
                                current_optimizer.best_params, test_data, actual_periods
                            )

                            test_text = f"测试集4位全中命中率: {test_score*100:.2f}%"
                            test_result_var.set(test_text)
                            add_log(f"手动停止时测试集命中率: {test_score*100:.1f}%")
                        else:
                            add_log("测试集数据不足或无法获取，无法评估")
                except Exception as e:
                    print(f"DEBUG: 手动停止时测试集评估出错: {e}")
                    add_log("测试集评估失败")
            else:
                add_log("未找到可应用的最佳参数")
                print("DEBUG: 停止优化时未找到可应用的参数")
            add_log("优化已停止")
        
        def close_dialog():
            """关闭对话框"""
            stop_optimization()
            if current_thread in self.running_threads:
                self.running_threads.remove(current_thread)
            dialog.destroy()
        
        def apply_best_params():
            """应用最佳参数"""
            add_log("=== 应用最佳参数按钮被点击 ===")
            print("DEBUG: apply_best_params函数被调用")

            best_params = None

            # 首先尝试从严格验证结果中获取最佳参数
            if hasattr(apply_best_params, 'optimization_results') and apply_best_params.optimization_results:
                best_params = apply_best_params.optimization_results.get('best_params')
                add_log("从严格验证结果中获取最佳参数")
                print(f"DEBUG: 从严格验证结果获取参数: {best_params is not None}")
            # 如果没有，尝试从优化器中获取
            elif current_optimizer and current_optimizer.best_params:
                best_params = current_optimizer.best_params
                add_log("从优化器中获取最佳参数")
                print(f"DEBUG: 从优化器获取参数: {best_params is not None}")

            print(f"DEBUG: 最终获取的参数: {best_params is not None}")

            if best_params:
                try:
                    # 保存最佳参数供回测使用
                    self.best_optimized_params = best_params.copy()
                    print("DEBUG: 已保存最佳参数供回测使用")

                    # 更新配置
                    self.config.update(best_params)
                    if save_config(self.config):
                        self.default_params = best_params

                        # 显示应用的参数详情
                        param_details = "\n".join([f"{k}: {v:.4f}" for k, v in best_params.items()])
                        success_message = f"已成功应用最佳参数！\n\n参数详情:\n{param_details}"

                        messagebox.showinfo("成功", success_message)
                        add_log("最佳参数已应用并保存")

                        # 应用成功后禁用按钮
                        apply_button.config(state='disabled', text="已应用参数",
                                          bg='#4CAF50', fg='white', cursor='arrow')
                    else:
                        messagebox.showerror("错误", "保存参数失败")
                        add_log("保存参数失败")
                except Exception as e:
                    error_msg = f"应用参数时发生错误: {str(e)}"
                    messagebox.showerror("错误", error_msg)
                    add_log(error_msg)
            else:
                messagebox.showwarning("警告", "没有找到可应用的最佳参数")
                add_log("警告：没有找到可应用的最佳参数")
        
        # 优化进度回调函数
        def progress_callback(iteration, total, current_best_rate, current_params):
            if not optimization_running:
                print("DEBUG: 检测到停止信号，终止进度回调")
                add_log("优化进度回调已停止")
                return False
            
            try:
                # 更新进度条
                progress = (iteration / total) * 100
                dialog.after(0, lambda: progress_var.set(progress))
                
                # 更新迭代信息
                status_text = f"迭代进度: {iteration}/{total} ({progress:.1f}%)"
                dialog.after(0, lambda: iteration_var.set(status_text))
                
                # 更新最佳结果（现在是测试集命中率，因为这是优化目标）
                result_text = f"测试集命中率: {current_best_rate*100:.2f}%"
                dialog.after(0, lambda: best_result_var.set(result_text))

                # 显示测试集命中率作为主要指标
                test_text = f"测试集4位全中命中率: {current_best_rate*100:.2f}%"
                dialog.after(0, lambda: test_result_var.set(test_text))

                # 每20次迭代评估训练集和验证集性能
                if iteration % 20 == 0 and iteration > 0 and current_params:
                    try:
                        # 评估训练集性能（监控是否欠拟合）
                        train_score = 0.0
                        if hasattr(progress_callback, 'train_data') and progress_callback.train_data is not None:
                            train_score = current_optimizer._evaluate_params_on_data(
                                current_params, progress_callback.train_data,
                                min(30, len(progress_callback.train_data)//4)
                            )

                        # 评估验证集性能（监控是否过拟合）
                        validation_score = 0.0
                        if hasattr(progress_callback, 'validation_data') and progress_callback.validation_data is not None:
                            validation_score = current_optimizer._evaluate_params_on_data(
                                current_params, progress_callback.validation_data,
                                min(15, len(progress_callback.validation_data)//3)
                            )

                        # 在日志中显示所有数据集表现
                        if iteration % 50 == 0:  # 每50次迭代记录一次
                            add_log(f"迭代{iteration}: 测试集{current_best_rate*100:.1f}%, 验证集{validation_score*100:.1f}%, 训练集{train_score*100:.1f}%")

                        # 过拟合检测：如果测试集比验证集高太多，可能过拟合
                        if validation_score > 0 and current_best_rate > validation_score + 0.15:  # 差异超过15%
                            print(f"DEBUG: 检测到可能的过拟合 - 测试集{current_best_rate*100:.1f}% vs 验证集{validation_score*100:.1f}%")
                            add_log(f"⚠️ 过拟合警告：测试集与验证集差异过大")

                        # 欠拟合检测：如果训练集比测试集低太多，可能欠拟合
                        if train_score > 0 and train_score < current_best_rate - 0.10:  # 差异超过10%
                            print(f"DEBUG: 检测到可能的欠拟合 - 训练集{train_score*100:.1f}% vs 测试集{current_best_rate*100:.1f}%")
                            add_log(f"⚠️ 欠拟合警告：训练集表现明显低于测试集")

                    except Exception as e:
                        print(f"DEBUG: 性能监控评估出错: {e}")
                        pass

                # 在优化50%时评估测试集命中率
                if hasattr(progress_callback, 'total_iterations') and progress_callback.total_iterations:
                    progress_percent = (iteration / progress_callback.total_iterations) * 100

                    # 调试：每10次迭代输出一次进度信息
                    if iteration % 10 == 0:
                        print(f"DEBUG: 迭代{iteration}/{progress_callback.total_iterations}, 进度{progress_percent:.1f}%")

                    # 当迭代进度达到50%时评估测试集
                    if progress_percent >= 50.0 and not hasattr(progress_callback, 'test_evaluated_at_50'):
                        print(f"DEBUG: 迭代进度达到50% ({progress_percent:.1f}%)，触发测试集评估")

                        # 检查所有必要条件
                        has_test_data = hasattr(progress_callback, 'test_data') and progress_callback.test_data is not None
                        has_current_params = current_params is not None

                        print(f"DEBUG: 测试数据可用: {has_test_data}")
                        print(f"DEBUG: 当前参数可用: {has_current_params}")

                        if has_test_data:
                            print(f"DEBUG: 测试数据大小: {len(progress_callback.test_data)}期")

                        if has_test_data and has_current_params:
                            try:
                                add_log("🎯 优化进度50%，评估测试集命中率...")
                                print("DEBUG: 开始50%进度测试集评估")

                                # 使用用户设置的回测期数
                                user_periods = getattr(progress_callback, 'user_backtest_periods', 20)
                                max_allowed = len(progress_callback.test_data) - 30
                                actual_periods = min(user_periods, max_allowed)

                                print(f"DEBUG: 50%评估使用回测期数: {actual_periods}期 (用户设置: {user_periods}期)")

                                test_score = current_optimizer._evaluate_params_on_data(
                                    current_params, progress_callback.test_data, actual_periods
                                )

                                print(f"DEBUG: 50%进度测试集评估完成，分数: {test_score:.3f}")

                                test_text = f"测试集4位全中命中率: {test_score*100:.2f}%"
                                print(f"DEBUG: 准备更新UI显示: {test_text}")

                                # 确保在主线程中更新UI
                                def update_test_result():
                                    test_result_var.set(test_text)
                                    print("DEBUG: UI已更新测试集命中率")

                                dialog.after(0, update_test_result)
                                add_log(f"50%进度测试集命中率: {test_score*100:.1f}%")

                                # 标记已评估，避免重复
                                progress_callback.test_evaluated_at_50 = True
                                print("DEBUG: 50%评估标记已设置")

                            except Exception as e:
                                print(f"DEBUG: 50%进度测试集评估出错: {e}")
                                add_log(f"50%进度评估失败: {e}")
                                import traceback
                                traceback.print_exc()
                        else:
                            print("DEBUG: 50%评估条件不满足，跳过")
                
                # 记录日志 - 修复条件判断
                should_log = False
                if iteration <= 10:  # 前10次迭代都记录
                    should_log = True
                elif iteration % 10 == 0:  # 之后每10次记录一次
                    should_log = True
                elif hasattr(current_optimizer, 'best_hit_rate') and current_best_rate > current_optimizer.best_hit_rate:  # 有改进时记录
                    should_log = True
                
                if should_log:
                    log_message = f"迭代 {iteration}:\n"
                    log_message += f"最佳命中率: {current_best_rate*100:.2f}%\n"
                    log_message += "当前最佳参数:\n"
                    for k, v in current_params.items():
                        log_message += f"  {k}: {v:.3f}\n"
                    log_message += "-" * 30 + "\n"
                    dialog.after(0, lambda msg=log_message: add_log(msg))
                
                # 强制更新UI
                dialog.after(0, lambda: dialog.update_idletasks())
                return True
            except Exception as e:
                print(f"更新进度时发生错误: {e}")
                return True  # 继续优化
        
        def run_optimization():
            """运行优化进程"""
            nonlocal optimization_running, current_thread, current_optimizer
            try:
                backtest_periods = backtest_periods_var.get()
                target_hit_rate = target_hit_rate_var.get() / 100.0
                max_iterations = max_iterations_var.get()
                population_size = population_size_var.get()
                num_threads = num_threads_var.get()
                
                # 验证参数
                if backtest_periods <= 0 or backtest_periods >= len(self.history_data):
                    messagebox.showwarning("警告", f"回测期数必须在1到{len(self.history_data)-1}之间")
                    return
                
                if target_hit_rate <= 0 or target_hit_rate > 1.0:
                    messagebox.showwarning("警告", "目标命中率必须在1-100%之间")
                    return
                
                if max_iterations <= 0:
                    messagebox.showwarning("警告", "最大迭代次数必须大于0")
                    return
                
                if population_size < 10:
                    messagebox.showwarning("警告", "种群大小必须至少为10")
                    return
                    
                if num_threads < 1:
                    messagebox.showwarning("警告", "线程数必须大于0")
                    return
                
                # 清空日志
                log_text.delete(1.0, tk.END)
                add_log("正在初始化优化过程...")
                add_log(f"使用 {num_threads} 个线程进行并行计算")
                
                # 禁用输入和开始按钮
                for widget in [backtest_periods_entry, target_hit_rate_entry, max_iterations_entry, 
                             population_size_entry, num_threads_entry, start_button]:
                    widget.config(state='disabled')
                
                # 启用停止按钮
                stop_button.config(state='normal')
                force_stop_button.config(state='normal')
                
                # 重置进度显示
                progress_var.set(0)
                iteration_var.set("准备开始优化...")
                best_result_var.set("测试集命中率: 0.00%")  # 现在优化目标是测试集
                validation_result_var.set("验证集命中率: --")
                test_result_var.set("测试集4位全中命中率: 0.00%")
                
                optimization_running = True
                
                # 创建优化器（使用新的严格验证模式）
                current_optimizer = ParameterOptimizer(
                    data_file="data.xlsx",
                    target_hit_rate=target_hit_rate,
                    max_iterations=max_iterations,
                    population_size=population_size,
                    num_threads=num_threads
                )

                # 更新验证配置
                current_optimizer.validation_config['min_backtest_periods'] = max(150, backtest_periods)

                # 设置回调函数
                current_optimizer.set_progress_callback(progress_callback)

                # 为进度回调函数设置数据（用于监控）
                # 先进行数据分割以获取训练、验证和测试数据
                total_periods = len(self.history_data)
                train_size = int(total_periods * 0.7)
                validation_size = int(total_periods * 0.15)

                train_data = self.history_data[:train_size]
                validation_data = self.history_data[train_size:train_size + validation_size]
                test_data = self.history_data[train_size + validation_size:]

                # 将数据和参数附加到回调函数
                progress_callback.train_data = train_data
                progress_callback.validation_data = validation_data
                progress_callback.test_data = test_data
                progress_callback.total_iterations = current_optimizer.max_iterations  # 从优化器获取
                progress_callback.user_backtest_periods = backtest_periods  # 用户设置的回测期数
                progress_callback.test_evaluated_at_50 = False  # 重置50%评估标记

                add_log("=" * 50)
                add_log("使用严格时间分割验证模式")
                add_log("=" * 50)
                add_log(f"最小回测期数: {current_optimizer.validation_config['min_backtest_periods']}")
                add_log(f"数据分割比例: 训练{current_optimizer.validation_config['train_ratio']:.0%}, "
                       f"验证{current_optimizer.validation_config['validation_ratio']:.0%}, "
                       f"测试{current_optimizer.validation_config['test_ratio']:.0%}")

                # 创建并启动严格验证优化线程
                def run_strict_optimization():
                    """运行严格时间分割验证的优化过程"""
                    try:
                        add_log("开始严格时间分割验证优化...")

                        # 使用新的严格验证方法，传递用户设置的回测期数
                        optimization_results = current_optimizer.optimize_with_strict_validation(self.history_data, backtest_periods)

                        if optimization_running and optimization_results:
                            # 显示详细结果
                            results = optimization_results

                            # 将优化结果存储到apply_best_params函数中，以便应用按钮使用
                            apply_best_params.optimization_results = optimization_results

                            add_log("\n" + "=" * 50)
                            add_log("优化完成！详细结果:")
                            add_log("=" * 50)

                            add_log(f"数据分割:")
                            add_log(f"  训练集: {results['data_split']['train_size']}期")
                            add_log(f"  验证集: {results['data_split']['validation_size']}期")
                            add_log(f"  测试集: {results['data_split']['test_size']}期")

                            add_log(f"\n性能结果:")
                            add_log(f"  训练集命中率: {results['train_score']:.3f} ({results['train_score']*100:.1f}%)")
                            add_log(f"  验证集命中率: {results['validation_score']:.3f} ({results['validation_score']*100:.1f}%)")
                            add_log(f"  测试集命中率: {results['test_score']:.3f} ({results['test_score']*100:.1f}%)")
                            add_log(f"  正则化分数: {results['regularized_score']:.3f} ({results['regularized_score']*100:.1f}%)")

                            # 随机基准比较
                            if results['random_baseline']:
                                baseline = results['random_baseline']
                                improvement = results['test_score'] - baseline['mean']
                                z_score = improvement / baseline['std'] if baseline['std'] > 0 else 0

                                add_log(f"\n与随机基准比较:")
                                add_log(f"  随机基准: {baseline['mean']:.3f} ({baseline['mean']*100:.1f}%)")
                                add_log(f"  性能提升: {improvement:.3f} ({improvement*100:.1f}%)")
                                add_log(f"  统计显著性: Z={z_score:.2f}")

                                if z_score > 1.96:
                                    add_log("  ✅ 模型显著优于随机基准 (p < 0.05)")
                                elif z_score > 1.0:
                                    add_log("  ⚠️ 模型略优于随机基准")
                                else:
                                    add_log("  ❌ 模型未优于随机基准")

                            # 参数稳定性
                            if results['stability_results']:
                                add_log(f"\n参数稳定性分析:")
                                stable_count = 0
                                total_count = 0
                                for param_name, stability in results['stability_results'].items():
                                    total_count += 1
                                    if stability['cv'] < 0.3:
                                        stable_count += 1
                                        status = "稳定"
                                    else:
                                        status = "不稳定"
                                    add_log(f"  {param_name}: 变异系数={stability['cv']:.3f} ({status})")

                                stability_ratio = stable_count / total_count if total_count > 0 else 0
                                add_log(f"  总体稳定性: {stable_count}/{total_count} ({stability_ratio:.1%})")

                            add_log(f"\n最佳参数:")
                            for param_name, value in results['best_params'].items():
                                add_log(f"  {param_name}: {value:.4f}")

                            # 更新进度显示
                            dialog.after(0, lambda: progress_var.set(100))
                            dialog.after(0, lambda: iteration_var.set("优化完成"))
                            dialog.after(0, lambda: best_result_var.set(f"测试集命中率: {results['test_score']*100:.2f}%"))

                            # 更新验证集和测试集显示
                            dialog.after(0, lambda: validation_result_var.set(f"验证集命中率: {results['validation_score']*100:.2f}%"))

                            # 调试：确认测试集结果
                            test_score_value = results['test_score']
                            print(f"DEBUG: 准备更新测试集显示，测试集分数: {test_score_value:.3f}")
                            dialog.after(0, lambda: test_result_var.set(f"测试集4位全中命中率: {test_score_value*100:.2f}%"))
                            print("DEBUG: 测试集显示更新命令已发送")

                            # 启用应用按钮
                            def enable_apply_button():
                                try:
                                    print("DEBUG: 尝试启用应用按钮")
                                    add_log("启用应用最佳参数按钮")

                                    # 应用启用样式
                                    apply_button.config(**apply_button.enabled_style)
                                    print(f"DEBUG: 按钮状态已设置为normal")
                                    print(f"DEBUG: 当前按钮状态: {apply_button['state']}")
                                    print(f"DEBUG: 当前按钮背景: {apply_button['bg']}")

                                    # 强制刷新按钮显示
                                    apply_button.update_idletasks()
                                    apply_button.update()
                                    print("DEBUG: 按钮显示已刷新")

                                    # 添加鼠标悬停效果测试
                                    def on_enter(event):
                                        _ = event  # 使用event参数避免警告
                                        if apply_button['state'] == 'normal':
                                            apply_button.config(bg='#1976D2')

                                    def on_leave(event):
                                        _ = event  # 使用event参数避免警告
                                        if apply_button['state'] == 'normal':
                                            apply_button.config(bg='#2196F3')

                                    apply_button.bind("<Enter>", on_enter)
                                    apply_button.bind("<Leave>", on_leave)

                                except Exception as e:
                                    print(f"DEBUG: 启用按钮时出错: {e}")
                                    add_log(f"启用按钮时出错: {e}")

                            dialog.after(0, enable_apply_button)

                            # 显示完成消息
                            result_summary = (f"严格验证优化完成!\n\n"
                                           f"测试集命中率: {results['test_score']*100:.2f}%\n"
                                           f"正则化分数: {results['regularized_score']*100:.2f}%\n\n"
                                           f"数据分割: 训练{results['data_split']['train_size']}期, "
                                           f"验证{results['data_split']['validation_size']}期, "
                                           f"测试{results['data_split']['test_size']}期")

                            dialog.after(0, lambda: messagebox.showinfo("优化完成", result_summary))

                        else:
                            add_log("优化被中断或失败")

                    except Exception as e:
                        add_log(f"优化过程中发生错误: {e}")
                        import traceback
                        add_log(f"错误详情: {traceback.format_exc()}")

                    finally:
                        # 恢复界面状态
                        dialog.after(0, lambda: [
                            widget.config(state='normal') for widget in
                            [backtest_periods_entry, target_hit_rate_entry, max_iterations_entry,
                             population_size_entry, num_threads_entry, start_button]
                        ])
                        dialog.after(0, lambda: stop_button.config(state='disabled'))

                # 创建并启动严格验证优化线程
                current_thread = threading.Thread(target=run_strict_optimization)
                current_thread.daemon = True
                self.running_threads.append(current_thread)
                current_thread.start()
                
            except ValueError:
                messagebox.showwarning("警告", "请输入有效的数值")
        

        
        # 创建按钮
        start_button = tk.Button(btn_frame, text="开始优化", command=run_optimization,
                               font=("SimHei", 10), bg="#4CAF50", fg="white",
                               relief=tk.RAISED, padx=20)
        start_button.pack(side=tk.LEFT, padx=5)
        
        stop_button = tk.Button(btn_frame, text="停止优化", command=stop_optimization,
                              font=("SimHei", 10), bg="#FF9800", fg="white",
                              relief=tk.RAISED, padx=20, state='disabled')
        stop_button.pack(side=tk.LEFT, padx=5)

        # 强制停止按钮
        def force_stop_optimization():
            """强制停止优化"""
            nonlocal optimization_running, current_thread, current_optimizer
            print("DEBUG: 强制停止优化被调用")
            add_log("⚠️ 强制停止优化...")

            optimization_running = False
            self.force_stop_optimization = True

            if current_optimizer:
                current_optimizer.optimization_running = False

            if current_thread and current_thread.is_alive():
                add_log("强制终止优化线程...")
                # 不等待，直接标记为停止
                current_thread = None

            add_log("⚠️ 已强制停止，建议重启程序确保完全清理")
            stop_button.config(state='disabled')
            force_stop_button.config(state='disabled')
            start_button.config(state='normal')

        force_stop_button = tk.Button(btn_frame, text="强制停止", command=force_stop_optimization,
                                     font=("SimHei", 10), bg="#d32f2f", fg="white",
                                     relief=tk.RAISED, padx=20, state='disabled')
        force_stop_button.pack(side=tk.LEFT, padx=5)
        
        # 测试按钮功能
        def test_apply_button():
            add_log("测试按钮被点击")
            print("DEBUG: 测试按钮功能正常")

            # 测试apply_best_params函数
            try:
                print("DEBUG: 尝试直接调用apply_best_params函数")
                apply_best_params()
                print("DEBUG: apply_best_params函数调用成功")
            except Exception as e:
                print(f"DEBUG: apply_best_params函数调用失败: {e}")
                add_log(f"apply_best_params函数调用失败: {e}")

            messagebox.showinfo("测试", "测试按钮功能正常，请查看日志")

        # 创建应用按钮的包装函数
        def apply_button_wrapper():
            try:
                print("DEBUG: 应用按钮包装函数被调用")
                add_log("应用按钮被点击")
                apply_best_params()
            except Exception as e:
                print(f"DEBUG: 应用按钮包装函数出错: {e}")
                add_log(f"应用按钮出错: {e}")
                messagebox.showerror("错误", f"应用参数时发生错误: {e}")

        apply_button = tk.Button(btn_frame, text="应用最佳参数", command=apply_button_wrapper,
                               font=("SimHei", 10), bg="#CCCCCC", fg="#666666",
                               relief=tk.RAISED, padx=20, state='disabled',
                               disabledforeground="#666666",
                               activebackground="#2196F3", activeforeground="white",
                               cursor="hand2")
        apply_button.pack(side=tk.LEFT, padx=5)

        # 存储按钮的启用和禁用样式
        apply_button.enabled_style = {
            'bg': '#2196F3', 'fg': 'white', 'state': 'normal',
            'relief': tk.RAISED, 'cursor': 'hand2'
        }
        apply_button.disabled_style = {
            'bg': '#CCCCCC', 'fg': '#666666', 'state': 'disabled',
            'relief': tk.RAISED, 'cursor': 'arrow'
        }

        # 添加测试按钮（临时调试用）
        test_button = tk.Button(btn_frame, text="测试按钮", command=test_apply_button,
                              font=("SimHei", 10), bg="#4CAF50", fg="white",
                              relief=tk.RAISED, padx=20)
        test_button.pack(side=tk.LEFT, padx=5)

        print(f"DEBUG: 按钮创建完成，初始状态: {apply_button['state']}")
        
        close_button = tk.Button(btn_frame, text="关闭窗口", command=close_dialog,
                               font=("SimHei", 10), bg="#f44336", fg="white",
                               relief=tk.RAISED, padx=20)
        close_button.pack(side=tk.LEFT, padx=5)
        
        # 设置对话框关闭事件
        dialog.protocol("WM_DELETE_WINDOW", close_dialog)

    def show_add_result_dialog(self, edit_period=None):
        """显示添加/修改开奖结果的对话框
        
        Args:
            edit_period: 如果提供，则为修改模式，否则为添加模式
        """
        # 检查data.xlsx是否存在
        data_file = "data.xlsx"
        try:
            df = pd.read_excel(data_file)
            if not all(col in df.columns for col in ['期号', '万位', '千位', '百位', '十位', '个位']):
                messagebox.showerror("错误", "data.xlsx文件格式不正确，请确保包含所有必要的列")
                return
            
            # 确保期号列为字符串类型
            df['期号'] = df['期号'].astype(str)
            
        except Exception as e:
            messagebox.showerror("错误", f"无法读取data.xlsx文件: {str(e)}")
            return
            
        dialog = tk.Toplevel(self.root)
        dialog.title("修改开奖号码" if edit_period else "录入开奖号码")
        dialog.geometry("400x300")
        dialog.resizable(False, False)
        dialog.transient(self.root)
        dialog.grab_set()
        
        # 创建输入框架
        input_frame = tk.Frame(dialog, padx=20, pady=10)
        input_frame.pack(fill=tk.BOTH, expand=True)
        
        # 期号输入
        period_frame = tk.Frame(input_frame)
        period_frame.pack(fill=tk.X, pady=5)
        tk.Label(period_frame, text="期号:", font=("SimHei", 10)).pack(side=tk.LEFT)
        period_var = tk.StringVar()
        
        if edit_period:
            # 修改模式：设置期号并禁用输入
            period_var.set(edit_period)
            period_entry = tk.Entry(period_frame, textvariable=period_var, width=15, state='readonly')
            # 获取当前号码
            row = df[df['期号'] == str(edit_period)].iloc[0]
            current_numbers = [row['万位'], row['千位'], row['百位'], row['十位'], row['个位']]
        else:
            # 添加模式：计算下一期号
            period_entry = tk.Entry(period_frame, textvariable=period_var, width=15)
            try:
                last_period = str(df['期号'].iloc[-1])
                if last_period:
                    import re
                    match = re.search(r'\d+', last_period)
                    if match:
                        number_part = match.group(0)
                        prefix = last_period[:last_period.index(number_part)]
                        suffix = last_period[last_period.index(number_part)+len(number_part):]
                        next_number = str(int(number_part) + 1).zfill(len(number_part))
                        next_period = f"{prefix}{next_number}{suffix}"
                    else:
                        next_period = str(int(last_period) + 1)
                    period_var.set(next_period)
            except:
                pass
            current_numbers = ['' for _ in range(5)]
            
        period_entry.pack(side=tk.LEFT, padx=5)
        
        # 号码输入框架
        numbers_frame = tk.Frame(input_frame)
        numbers_frame.pack(fill=tk.X, pady=10)
        
        # 创建5个数字输入框
        number_vars = []
        number_entries = []
        position_names = ['万位', '千位', '百位', '十位', '个位']
        
        for i, pos_name in enumerate(position_names):
            pos_frame = tk.Frame(numbers_frame)
            pos_frame.pack(pady=5)
            
            tk.Label(pos_frame, text=f"{pos_name}:", font=("SimHei", 10)).pack(side=tk.LEFT)
            var = tk.StringVar(value=str(current_numbers[i]))
            number_vars.append(var)
            entry = tk.Entry(pos_frame, textvariable=var, width=5)
            entry.pack(side=tk.LEFT, padx=5)
            number_entries.append(entry)
            
            # 添加验证
            def validate_number(P):
                if P == "": return True
                return P.isdigit() and len(P) <= 1 and 0 <= int(P) <= 9
            vcmd = (dialog.register(validate_number), '%P')
            entry.config(validate='key', validatecommand=vcmd)
        
        # 设置焦点移动
        def focus_next(_):
            current = dialog.focus_get()
            if current in number_entries:
                idx = number_entries.index(current)
                if idx < len(number_entries) - 1:
                    number_entries[idx + 1].focus()
                    return "break"
            return None
            
        for entry in number_entries:
            entry.bind('<Key-Return>', focus_next)
            entry.bind('<Key-Tab>', focus_next)
        
        # 保存按钮
        def save_result():
            # 验证输入
            if not period_var.get().strip():
                messagebox.showwarning("警告", "请输入期号")
                return
                
            numbers = []
            for var in number_vars:
                if not var.get().strip():
                    messagebox.showwarning("警告", "请输入完整的开奖号码")
                    return
                try:
                    num = int(var.get())
                    if not (0 <= num <= 9):
                        raise ValueError
                    numbers.append(num)
                except ValueError:
                    messagebox.showwarning("警告", "号码必须是0-9之间的数字")
                    return
            
            try:
                # 读取现有Excel文件
                df = pd.read_excel(data_file)
                
                # 确保期号列为字符串类型
                df['期号'] = df['期号'].astype(str)
                current_period = str(period_var.get()).strip()
                
                if edit_period:
                    # 修改模式：更新现有行
                    mask = df['期号'] == current_period
                    df.loc[mask, ['万位', '千位', '百位', '十位', '个位']] = numbers
                else:
                    # 添加模式：检查期号是否已存在
                    if current_period in df['期号'].values:
                        messagebox.showerror("错误", "该期号已存在，每个期号必须是唯一的")
                        return
                    
                    # 创建新行数据
                    new_data = {
                        '期号': current_period,
                        '万位': numbers[0],
                        '千位': numbers[1],
                        '百位': numbers[2],
                        '十位': numbers[3],
                        '个位': numbers[4]
                    }
                    
                    # 直接使用loc添加新行到最后
                    df.loc[len(df)] = new_data
                
                # 保存到Excel，保持原有顺序
                df.to_excel(data_file, index=False)
                
                messagebox.showinfo("成功", "开奖号码已保存")
                dialog.destroy()
                
                # 重新加载数据
                self.file_path_var.set(data_file)
                self.auto_load_last_file()
                
            except Exception as e:
                messagebox.showerror("错误", f"保存失败: {str(e)}")
        
        btn_frame = tk.Frame(input_frame)
        btn_frame.pack(fill=tk.X, pady=20)
        
        save_btn = tk.Button(btn_frame, text="保存", command=save_result,
                           bg="#4CAF50", fg="white", font=("SimHei", 10),
                           relief=tk.RAISED, padx=20)
        save_btn.pack(side=tk.LEFT, padx=10)
        
        cancel_btn = tk.Button(btn_frame, text="取消", command=dialog.destroy,
                             bg="#f44336", fg="white", font=("SimHei", 10),
                             relief=tk.RAISED, padx=20)
        cancel_btn.pack(side=tk.LEFT, padx=10)
        
        # 设置初始焦点
        if edit_period:
            number_entries[0].focus()
        else:
            if not period_var.get():
                period_entry.focus()
            else:
                number_entries[0].focus()

    def test_prediction_consistency(self):
        """测试预测一致性的GUI方法"""
        if self.model is None or self.history_data is None:
            messagebox.showwarning("警告", "请先加载数据并进行回测")
            return

        # 在新线程中运行测试，避免界面卡顿
        def run_test():
            try:
                self.status_var.set("正在测试预测一致性...")
                self.root.update()

                # 运行一致性测试
                is_consistent, predictions_list = self.model.test_prediction_consistency(num_tests=5)

                # 在主线程中显示结果
                def show_result():
                    if is_consistent:
                        messagebox.showinfo("测试结果",
                                          "✓ 预测结果一致！随机性问题已解决。\n"
                                          f"所有5次测试的预测结果完全相同：\n{predictions_list[0]}")
                    else:
                        result_text = "✗ 预测结果不一致，仍存在随机性问题。\n\n"
                        for i, pred in enumerate(predictions_list, 1):
                            result_text += f"测试 {i}: {pred}\n"
                        messagebox.showwarning("测试结果", result_text)

                    self.status_var.set("就绪")

                self.root.after(0, show_result)

            except Exception as e:
                def show_error():
                    messagebox.showerror("错误", f"测试失败: {str(e)}")
                    self.status_var.set("就绪")
                self.root.after(0, show_error)

        threading.Thread(target=run_test).start()



    def create_online_learning_ui(self):
        """创建在线学习UI"""
        # 控制面板
        control_frame = tk.Frame(self.online_learning_frame, bg="white", padx=20, pady=10)
        control_frame.pack(fill=tk.X)

        # 在线学习开关
        self.online_learning_var = tk.BooleanVar()
        online_learning_check = tk.Checkbutton(control_frame, text="启用在线学习",
                                             variable=self.online_learning_var,
                                             command=self.toggle_online_learning,
                                             bg="white", font=("SimHei", 10))
        online_learning_check.pack(side=tk.LEFT, padx=5)

        # 初始化按钮
        init_btn = tk.Button(control_frame, text="初始化在线学习",
                           command=self.initialize_online_learning_ui,
                           font=("SimHei", 10), bg="#4CAF50", fg="white", padx=10)
        init_btn.pack(side=tk.LEFT, padx=5)

        # 性能摘要按钮
        summary_btn = tk.Button(control_frame, text="查看性能摘要",
                              command=self.show_online_learning_summary,
                              font=("SimHei", 10), bg="#2196F3", fg="white", padx=10)
        summary_btn.pack(side=tk.LEFT, padx=5)

        # 状态显示
        self.online_learning_status_var = tk.StringVar()
        self.online_learning_status_var.set("在线学习未启用")
        status_label = tk.Label(control_frame, textvariable=self.online_learning_status_var,
                              bg="white", font=("SimHei", 10), fg="blue")
        status_label.pack(side=tk.LEFT, padx=10)

        # 信息显示区域
        info_frame = tk.Frame(self.online_learning_frame, bg="white", padx=20, pady=10)
        info_frame.pack(fill=tk.BOTH, expand=True)

        # 创建滚动文本框显示在线学习信息
        self.online_learning_text = scrolledtext.ScrolledText(info_frame, height=20, width=80,
                                                            font=("Consolas", 10))
        self.online_learning_text.pack(fill=tk.BOTH, expand=True)

        # 添加初始说明
        initial_text = """在线学习系统说明：

1. 功能概述：
   - 根据预测结果自动调整模型参数
   - 实时监控预测性能
   - 当性能下降时自动优化参数

2. 使用步骤：
   - 可以直接使用配置文件中的现有参数，无需重新优化
   - 点击"初始化在线学习"按钮（会自动选择最佳可用参数）
   - 勾选"启用在线学习"开关
   - 进行预测时系统会自动记录结果并调整参数

3. 参数来源优先级：
   - 最佳优化参数（如果已完成参数优化）
   - 配置文件参数（程序启动时自动加载）
   - 默认参数（作为备选）

4. 调整策略：
   - 核心参数调整：alpha、lambda
   - 权重分布调整：短期、中期、长期、协同权重
   - 温度参数调整：热数、冷数倍数

5. 性能监控：
   - 最少10次预测后开始调整
   - 每20次预测评估一次性能
   - 性能下降超过2%时触发参数调整

💡 提示：现在可以直接点击"初始化在线学习"开始使用！
"""
        self.online_learning_text.insert(tk.END, initial_text)
        self.online_learning_text.config(state=tk.DISABLED)

    def toggle_online_learning(self):
        """切换在线学习开关"""
        if self.online_learning_var.get():
            if self.online_learning_system is None:
                messagebox.showwarning("警告", "请先初始化在线学习系统")
                self.online_learning_var.set(False)
                return

            self.online_learning_enabled = True
            self.online_learning_status_var.set("在线学习已启用")
            self.log_online_learning("在线学习已启用")
        else:
            self.online_learning_enabled = False
            self.online_learning_status_var.set("在线学习已禁用")
            self.log_online_learning("在线学习已禁用")

    def initialize_online_learning_ui(self):
        """初始化在线学习系统UI"""
        # 优先使用最佳优化参数，如果没有则使用配置文件中的参数
        params_to_use = None
        params_source = ""

        if self.best_optimized_params is not None:
            params_to_use = self.best_optimized_params
            params_source = "最佳优化参数"
        elif self.config:
            # 从配置文件中提取模型参数（排除非模型参数）
            model_param_keys = ['alpha', 'lambda', 'short_weight', 'mid_weight', 'long_weight',
                              'co_weight', 'hot_threshold', 'cold_threshold', 'hot_multiplier',
                              'cold_multiplier', 'window', 'periodicity', 'selection_count']
            params_to_use = {k: v for k, v in self.config.items() if k in model_param_keys}
            params_source = "配置文件参数"
        else:
            # 使用默认参数
            params_to_use = self.default_params
            params_source = "默认参数"

        if not params_to_use:
            messagebox.showerror("错误", "无法获取有效的模型参数")
            return

        # 初始化在线学习系统
        self.online_learning_system = OnlineLearningSystem(params_to_use)
        self.online_learning_status_var.set("在线学习系统已初始化")

        self.log_online_learning(f"在线学习系统初始化成功（使用{params_source}）")
        self.log_online_learning(f"使用参数: {params_to_use}")

        messagebox.showinfo("成功", f"在线学习系统初始化成功！\n使用{params_source}\n现在可以启用在线学习开关。")

    def show_online_learning_summary(self):
        """显示在线学习性能摘要"""
        if self.online_learning_system is None:
            messagebox.showwarning("警告", "在线学习系统未初始化")
            return

        summary = self.online_learning_system.get_performance_summary()
        if isinstance(summary, str):
            messagebox.showinfo("性能摘要", summary)
        else:
            summary_text = f"""在线学习性能摘要：

总预测次数: {summary['total_predictions']}
整体性能: {summary['overall_performance']:.3f}
近期性能: {summary['recent_performance']:.3f}
性能趋势: {summary['performance_trend']:+.3f}
参数调整次数: {summary['adaptations_count']}
"""
            messagebox.showinfo("性能摘要", summary_text)

    def log_online_learning(self, message):
        """记录在线学习日志"""
        timestamp = datetime.now().strftime("%H:%M:%S")
        log_message = f"[{timestamp}] {message}\n"

        self.online_learning_text.config(state=tk.NORMAL)
        self.online_learning_text.insert(tk.END, log_message)
        self.online_learning_text.see(tk.END)
        self.online_learning_text.config(state=tk.DISABLED)

    def record_prediction_for_online_learning(self, prediction, actual_result):
        """为在线学习记录预测结果"""
        if self.online_learning_enabled and self.online_learning_system:
            self.prediction_count += 1

            # 记录预测结果
            self.online_learning_system.record_prediction_result(prediction, actual_result)

            # 获取更新后的参数
            updated_params = self.online_learning_system.get_current_params()

            # 更新默认参数
            self.default_params.update(updated_params)

            # 记录日志
            hit_rate = self.online_learning_system.performance_history[-1]
            self.log_online_learning(f"预测#{self.prediction_count}: 命中率 {hit_rate:.3f}")

            # 如果参数有调整，记录调整信息
            if len(self.online_learning_system.performance_history) >= 10:
                recent_adjustments = getattr(self.online_learning_system, 'last_adaptation_time', 0)
                if recent_adjustments > 0:
                    self.log_online_learning("参数已根据性能自动调整")

def main():
    root = tk.Tk()
    try:
        # 尝试设置图标，如果失败就忽略
        root.iconbitmap("")
    except:
        pass  # 忽略图标设置错误
    LotteryPredictionApp(root)
    root.mainloop()

if __name__ == "__main__":
    main()
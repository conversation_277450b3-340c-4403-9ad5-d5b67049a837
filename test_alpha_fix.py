#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试alpha <= 0错误修复
"""

import numpy as np
import random

def test_dirichlet_generation():
    """测试Dirichlet分布生成"""
    print("测试Dirichlet分布生成...")
    
    # 测试各种权重情况
    test_cases = [
        np.array([0.25, 0.25, 0.25, 0.25]),  # 正常权重
        np.array([0.0, 0.5, 0.3, 0.2]),      # 包含0的权重
        np.array([0.1, 0.0, 0.0, 0.9]),      # 多个0权重
        np.array([-0.1, 0.5, 0.3, 0.3]),     # 包含负数
        np.array([0.0, 0.0, 0.0, 0.0]),      # 全0权重
    ]
    
    for i, weights in enumerate(test_cases):
        print(f"\n测试案例 {i+1}: {weights}")
        
        # 应用修复逻辑
        weights_fixed = np.maximum(weights, 0.001)  # 最小值为0.001
        weights_fixed = weights_fixed / np.sum(weights_fixed)   # 重新归一化
        
        # 使用Dirichlet分布进行变异
        alpha = weights_fixed * 10 + 0.1  # 确保alpha > 0.1
        
        # 双重检查alpha是否有效
        if np.any(alpha <= 0) or np.any(np.isnan(alpha)) or np.any(np.isinf(alpha)):
            # 如果权重无效，使用默认的均匀分布
            alpha = np.array([1.0, 1.0, 1.0, 1.0])
            print(f"  使用默认alpha: {alpha}")
        else:
            print(f"  修复后权重: {weights_fixed}")
            print(f"  计算的alpha: {alpha}")
        
        try:
            new_weights = np.random.dirichlet(alpha)
            print(f"  生成的新权重: {new_weights}")
            print(f"  权重和: {np.sum(new_weights):.6f}")
            print("  ✅ 成功生成")
        except ValueError as e:
            print(f"  ❌ 仍然失败: {e}")
            # 使用默认权重
            new_weights = np.array([0.25, 0.25, 0.25, 0.25])
            print(f"  使用默认权重: {new_weights}")

def test_weight_range_validation():
    """测试权重范围验证"""
    print("\n" + "="*50)
    print("测试权重范围验证...")
    
    # 模拟参数范围
    param_ranges = {
        'short_weight': (0.01, 1.0),
        'mid_weight': (0.01, 1.0),
        'long_weight': (0.01, 1.0),
        'co_weight': (0.01, 1.0),
    }
    
    # 测试权重生成
    for attempt in range(10):
        print(f"\n尝试 {attempt + 1}:")
        
        # 生成Dirichlet权重
        weights = np.random.dirichlet(np.array([2, 3, 4, 2]))
        short_w, mid_w, long_w, co_w = weights
        
        print(f"  原始权重: [{short_w:.3f}, {mid_w:.3f}, {long_w:.3f}, {co_w:.3f}]")
        print(f"  权重和: {np.sum(weights):.6f}")
        
        # 检查是否在允许范围内
        in_range = (param_ranges['short_weight'][0] <= short_w <= param_ranges['short_weight'][1] and
                   param_ranges['mid_weight'][0] <= mid_w <= param_ranges['mid_weight'][1] and
                   param_ranges['long_weight'][0] <= long_w <= param_ranges['long_weight'][1] and
                   param_ranges['co_weight'][0] <= co_w <= param_ranges['co_weight'][1])
        
        if in_range:
            print("  ✅ 权重在有效范围内")
        else:
            print("  ⚠️ 权重超出范围，需要调整")
            
            # 应用最小值约束
            weights_adjusted = np.maximum(weights, 0.01)
            weights_adjusted = weights_adjusted / np.sum(weights_adjusted)
            
            print(f"  调整后权重: [{weights_adjusted[0]:.3f}, {weights_adjusted[1]:.3f}, {weights_adjusted[2]:.3f}, {weights_adjusted[3]:.3f}]")
            print(f"  调整后权重和: {np.sum(weights_adjusted):.6f}")

def test_edge_cases():
    """测试边界情况"""
    print("\n" + "="*50)
    print("测试边界情况...")
    
    edge_cases = [
        "极小权重",
        "极大权重差异", 
        "接近边界值",
        "数值精度问题"
    ]
    
    test_weights = [
        np.array([0.001, 0.001, 0.001, 0.997]),  # 极小权重
        np.array([0.01, 0.01, 0.01, 0.97]),      # 极大权重差异
        np.array([0.01, 0.99, 0.005, 0.005]),    # 接近边界值
        np.array([1e-10, 0.333333, 0.333333, 0.333334])  # 数值精度问题
    ]
    
    for i, (case_name, weights) in enumerate(zip(edge_cases, test_weights)):
        print(f"\n{case_name}: {weights}")
        
        # 应用完整的修复逻辑
        weights_safe = np.maximum(weights, 0.001)
        weights_safe = weights_safe / np.sum(weights_safe)
        
        alpha = weights_safe * 10 + 0.1
        
        if np.any(alpha <= 0) or np.any(np.isnan(alpha)) or np.any(np.isinf(alpha)):
            alpha = np.array([1.0, 1.0, 1.0, 1.0])
            print("  使用默认alpha")
        
        try:
            result = np.random.dirichlet(alpha)
            print(f"  ✅ 成功: {result}")
        except Exception as e:
            print(f"  ❌ 失败: {e}")

if __name__ == "__main__":
    print("🔧 测试alpha <= 0错误修复\n")
    
    # 设置随机种子以获得可重现的结果
    random.seed(12345)
    np.random.seed(12345)
    
    try:
        test_dirichlet_generation()
        test_weight_range_validation()
        test_edge_cases()
        
        print("\n" + "="*50)
        print("🎉 所有测试完成！")
        print("✅ alpha <= 0 错误已修复")
        print("✅ 权重生成更加稳定")
        print("✅ 边界情况处理完善")
        
    except Exception as e:
        print(f"\n❌ 测试过程中发生错误: {e}")
        import traceback
        traceback.print_exc()

import numpy as np

# 测试修复逻辑
print("测试alpha修复...")

# 模拟问题权重
problem_weights = [
    np.array([0.0, 0.5, 0.3, 0.2]),
    np.array([0.1, 0.0, 0.0, 0.9]),
    np.array([-0.1, 0.5, 0.3, 0.3])
]

for i, weights in enumerate(problem_weights):
    print(f"\n测试 {i+1}: {weights}")
    
    # 应用修复逻辑
    weights_fixed = np.maximum(weights, 0.001)
    weights_fixed = weights_fixed / np.sum(weights_fixed)
    alpha = weights_fixed * 10 + 0.1
    
    print(f"修复后: {weights_fixed}")
    print(f"Alpha: {alpha}")
    
    try:
        result = np.random.dirichlet(alpha)
        print(f"成功: {result}")
    except Exception as e:
        print(f"失败: {e}")

print("\n修复测试完成")

#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简单UI测试
"""

import tkinter as tk
import sys
import traceback

def test_simple_ui():
    """简单UI测试"""
    print("开始简单UI测试...")
    
    try:
        # 导入主程序
        sys.path.insert(0, '.')
        
        # 测试导入
        print("1. 测试导入...")
        import importlib
        spec = importlib.import_module('67(3)')
        print("✅ 主模块导入成功")
        
        # 测试类获取
        print("2. 测试类获取...")
        LotteryPredictionApp = getattr(spec, 'LotteryPredictionApp')
        print("✅ LotteryPredictionApp类获取成功")
        
        # 测试窗口创建
        print("3. 测试窗口创建...")
        root = tk.Tk()
        root.title("测试窗口")
        root.geometry("300x200")
        print("✅ 根窗口创建成功")
        
        # 测试应用创建
        print("4. 测试应用创建...")
        app = LotteryPredictionApp(root)
        print("✅ 应用程序创建成功")
        
        # 检查关键属性
        print("5. 检查关键属性...")
        key_attrs = ['root', 'notebook', 'file_path_var', 'status_var']
        for attr in key_attrs:
            if hasattr(app, attr):
                print(f"✅ {attr} 存在")
            else:
                print(f"❌ {attr} 缺失")
        
        # 立即关闭
        print("6. 关闭窗口...")
        root.destroy()
        print("✅ 窗口关闭成功")
        
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        traceback.print_exc()
        return False

if __name__ == "__main__":
    print("=" * 40)
    print("简单UI测试")
    print("=" * 40)
    
    success = test_simple_ui()
    
    if success:
        print("\n🎉 测试成功！程序应该可以正常显示UI。")
    else:
        print("\n❌ 测试失败！存在问题需要修复。")

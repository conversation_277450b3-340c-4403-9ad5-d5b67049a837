#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试UI显示功能
"""

import tkinter as tk
from tkinter import ttk
import time
import threading

def test_optimization_display():
    """测试优化过程中的显示效果"""
    root = tk.Tk()
    root.title("优化显示测试")
    root.geometry("600x400")
    
    # 创建主框架
    main_frame = tk.Frame(root)
    main_frame.pack(fill=tk.BOTH, expand=True, padx=20, pady=20)
    
    # 标题
    title_label = tk.Label(main_frame, text="参数优化显示测试", 
                          font=("SimHei", 16, "bold"), fg="#2196F3")
    title_label.pack(pady=10)
    
    # 进度框架
    progress_frame = tk.Frame(main_frame, relief=tk.RIDGE, bd=2, padx=10, pady=10)
    progress_frame.pack(fill=tk.X, pady=10)
    
    # 进度条
    progress_var = tk.DoubleVar()
    progress_bar = ttk.Progressbar(progress_frame, variable=progress_var, maximum=100)
    progress_bar.pack(fill=tk.X, pady=5)
    
    # 迭代信息
    iteration_var = tk.StringVar(value="准备开始优化...")
    iteration_label = tk.Label(progress_frame, textvariable=iteration_var, font=("SimHei", 10))
    iteration_label.pack(pady=5)
    
    # 当前最佳结果（训练集）
    best_result_var = tk.StringVar(value="训练集命中率: 0.00%")
    best_result_label = tk.Label(progress_frame, textvariable=best_result_var, font=("SimHei", 10))
    best_result_label.pack(pady=5)
    
    # 验证集命中率
    validation_result_var = tk.StringVar(value="验证集命中率: --")
    validation_result_label = tk.Label(progress_frame, textvariable=validation_result_var, 
                                     font=("SimHei", 10), fg="#FF9800")
    validation_result_label.pack(pady=2)
    
    # 测试集4位全中命中率
    test_result_var = tk.StringVar(value="测试集4位全中命中率: --")
    test_result_label = tk.Label(progress_frame, textvariable=test_result_var, 
                               font=("SimHei", 10), fg="#4CAF50", relief=tk.RIDGE, padx=5)
    test_result_label.pack(pady=2)
    
    # 状态显示
    status_frame = tk.Frame(main_frame)
    status_frame.pack(fill=tk.X, pady=10)
    
    status_var = tk.StringVar(value="点击开始测试按钮")
    status_label = tk.Label(status_frame, textvariable=status_var, font=("SimHei", 12))
    status_label.pack()
    
    # 模拟优化过程
    def simulate_optimization():
        """模拟优化过程"""
        status_var.set("开始模拟优化过程...")
        
        # 模拟数据
        max_iterations = 100
        train_rates = [0.15, 0.25, 0.35, 0.45, 0.52, 0.58, 0.62, 0.65, 0.67, 0.68]
        validation_rates = [0.12, 0.20, 0.28, 0.32, 0.35, 0.38, 0.40, 0.38, 0.36, 0.35]
        
        for i in range(max_iterations):
            if not hasattr(simulate_optimization, 'running') or not simulate_optimization.running:
                break
                
            # 更新进度
            progress = (i + 1) / max_iterations * 100
            progress_var.set(progress)
            
            # 更新迭代信息
            iteration_var.set(f"迭代进度: {i+1}/{max_iterations} ({progress:.1f}%)")
            
            # 更新训练集命中率（逐渐提高）
            if i < len(train_rates):
                train_rate = train_rates[i]
            else:
                train_rate = train_rates[-1] + (i - len(train_rates)) * 0.001
            
            best_result_var.set(f"训练集命中率: {train_rate*100:.2f}%")
            
            # 每10次迭代更新验证集命中率
            if (i + 1) % 10 == 0:
                val_idx = min((i + 1) // 10 - 1, len(validation_rates) - 1)
                validation_rate = validation_rates[val_idx]
                validation_result_var.set(f"验证集命中率: {validation_rate*100:.2f}%")
                status_var.set(f"第{i+1}次迭代 - 验证集评估完成")
            
            root.update_idletasks()
            time.sleep(0.05)  # 模拟计算时间
        
        # 优化完成
        if hasattr(simulate_optimization, 'running') and simulate_optimization.running:
            iteration_var.set("优化完成")
            progress_var.set(100)
            
            # 最终结果
            final_train = 0.68
            final_validation = 0.35
            final_test = 0.267
            
            best_result_var.set(f"训练集命中率: {final_train*100:.2f}%")
            validation_result_var.set(f"验证集命中率: {final_validation*100:.2f}%")
            test_result_var.set(f"测试集4位全中命中率: {final_test*100:.2f}%")
            
            status_var.set("✅ 优化完成！所有指标已更新")
        else:
            status_var.set("❌ 优化被中断")
    
    # 控制按钮
    button_frame = tk.Frame(main_frame)
    button_frame.pack(fill=tk.X, pady=20)
    
    def start_simulation():
        """开始模拟"""
        simulate_optimization.running = True
        
        # 重置显示
        progress_var.set(0)
        iteration_var.set("准备开始优化...")
        best_result_var.set("训练集命中率: 0.00%")
        validation_result_var.set("验证集命中率: --")
        test_result_var.set("测试集4位全中命中率: --")
        
        # 在新线程中运行模拟
        thread = threading.Thread(target=simulate_optimization)
        thread.daemon = True
        thread.start()
    
    def stop_simulation():
        """停止模拟"""
        simulate_optimization.running = False
        status_var.set("模拟已停止")
    
    def reset_display():
        """重置显示"""
        simulate_optimization.running = False
        progress_var.set(0)
        iteration_var.set("准备开始优化...")
        best_result_var.set("训练集命中率: 0.00%")
        validation_result_var.set("验证集命中率: --")
        test_result_var.set("测试集4位全中命中率: --")
        status_var.set("显示已重置")
    
    tk.Button(button_frame, text="开始模拟", command=start_simulation,
             bg="#4CAF50", fg="white", font=("SimHei", 10), padx=20).pack(side=tk.LEFT, padx=5)
    
    tk.Button(button_frame, text="停止模拟", command=stop_simulation,
             bg="#FF9800", fg="white", font=("SimHei", 10), padx=20).pack(side=tk.LEFT, padx=5)
    
    tk.Button(button_frame, text="重置显示", command=reset_display,
             bg="#2196F3", fg="white", font=("SimHei", 10), padx=20).pack(side=tk.LEFT, padx=5)
    
    tk.Button(button_frame, text="关闭", command=root.destroy,
             bg="#f44336", fg="white", font=("SimHei", 10), padx=20).pack(side=tk.LEFT, padx=5)
    
    # 说明文字
    info_text = """
测试说明：
1. 训练集命中率：显示当前遗传算法找到的最佳参数在训练数据上的表现
2. 验证集命中率：每10次迭代评估一次，用于监控过拟合情况
3. 测试集4位全中命中率：只在优化完成后显示，代表真实预测能力

颜色说明：
- 黑色：训练集命中率（基础指标）
- 橙色：验证集命中率（中间监控）
- 绿色：测试集命中率（最终评估，带边框突出显示）
"""
    
    info_label = tk.Label(main_frame, text=info_text, font=("SimHei", 9), 
                         justify=tk.LEFT, fg="gray")
    info_label.pack(pady=10)
    
    root.mainloop()

if __name__ == "__main__":
    print("🎨 UI显示测试")
    print("测试优化过程中的各种命中率显示")
    
    try:
        test_optimization_display()
        print("✅ UI显示测试完成")
    except Exception as e:
        print(f"❌ 测试过程中发生错误: {e}")
        import traceback
        traceback.print_exc()

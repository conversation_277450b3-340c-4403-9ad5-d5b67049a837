#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试评估函数修复
"""

import pandas as pd
import numpy as np
import sys
import os

# 添加当前目录到路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_evaluation_fix():
    """测试评估函数修复"""
    print("=" * 60)
    print("测试评估函数修复")
    print("=" * 60)
    
    try:
        # 导入主模块
        print("0. 导入主模块...")
        try:
            exec(open('67(3).py', encoding='utf-8').read(), globals())
            print("   成功导入主模块")
        except Exception as e:
            print(f"   导入主模块失败: {e}")
            return False
        
        # 1. 加载测试数据
        print("\n1. 加载测试数据...")
        try:
            # 尝试加载数据文件
            if os.path.exists('data.xlsx'):
                df = pd.read_excel('data.xlsx')
                print(f"   成功加载 data.xlsx，数据形状: {df.shape}")
            elif os.path.exists('test_final_data.xlsx'):
                df = pd.read_excel('test_final_data.xlsx')
                print(f"   成功加载 test_final_data.xlsx，数据形状: {df.shape}")
            else:
                # 生成模拟数据
                print("   未找到数据文件，生成模拟数据...")
                np.random.seed(42)
                data = []
                for i in range(200):  # 生成200期数据
                    row = [np.random.randint(0, 10) for _ in range(5)]
                    data.append(row)
                df = pd.DataFrame(data, columns=['位置1', '位置2', '位置3', '位置4', '位置5'])
                print(f"   生成模拟数据，形状: {df.shape}")
            
            # 转换为数组格式
            historical_data = df.iloc[:, :5].values.tolist()
            print(f"   历史数据转换完成，长度: {len(historical_data)}")
            
        except Exception as e:
            print(f"   数据加载失败: {e}")
            return False
        
        # 2. 测试参数生成
        print("\n2. 测试参数生成...")
        try:
            optimizer = ParameterOptimizer('data.xlsx')
            test_params = optimizer._generate_random_params()
            print(f"   参数生成成功，参数数量: {len(test_params)}")
            print(f"   关键参数: alpha={test_params.get('alpha', 'N/A'):.3f}, "
                  f"lambda={test_params.get('lambda', 'N/A'):.3f}")
            print(f"   权重参数: short={test_params.get('short_weight', 'N/A'):.3f}, "
                  f"mid={test_params.get('mid_weight', 'N/A'):.3f}")
        except Exception as e:
            print(f"   参数生成失败: {e}")
            import traceback
            traceback.print_exc()
            return False
        
        # 3. 测试模型创建和训练
        print("\n3. 测试模型创建和训练...")
        try:
            model = MFTNModel(test_params)
            train_data = historical_data[:100]  # 使用前100期作为训练数据
            model.fit(train_data)
            print(f"   模型训练成功，训练数据长度: {len(train_data)}")
        except Exception as e:
            print(f"   模型训练失败: {e}")
            import traceback
            traceback.print_exc()
            return False
        
        # 4. 测试预测功能
        print("\n4. 测试预测功能...")
        try:
            predictions = model.predict_next()
            print(f"   预测成功，预测结果: {predictions}")
            
            # 验证预测格式
            if isinstance(predictions, dict) and len(predictions) > 0:
                for pos, pred in predictions.items():
                    if isinstance(pred, (list, tuple)) and len(pred) > 0:
                        print(f"   位置{pos}: {pred} ✓")
                    else:
                        print(f"   位置{pos}: 格式无效 ✗")
                        return False
            else:
                print(f"   预测结果格式无效: {type(predictions)}")
                return False
                
        except Exception as e:
            print(f"   预测失败: {e}")
            import traceback
            traceback.print_exc()
            return False
        
        # 5. 测试评估函数
        print("\n5. 测试评估函数...")
        try:
            # 使用较小的回测期数进行测试
            backtest_periods = 10
            result = optimizer._evaluate_params_on_data(test_params, historical_data, backtest_periods)
            print(f"   评估完成，结果: {result:.3f}")
            
            if result > 0:
                print(f"   ✅ 评估函数工作正常，命中率: {result*100:.1f}%")
            elif result == 0:
                print(f"   ⚠️ 评估结果为0，可能是正常情况（命中率确实为0）")
            else:
                print(f"   ❌ 评估结果异常: {result}")
                return False
                
        except Exception as e:
            print(f"   评估函数测试失败: {e}")
            import traceback
            traceback.print_exc()
            return False
        
        print("\n" + "=" * 60)
        print("✅ 所有测试通过！评估函数修复成功")
        print("=" * 60)
        return True
        
    except Exception as e:
        print(f"\n❌ 测试过程中出现错误: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = test_evaluation_fix()
    if success:
        print("\n🎉 评估函数修复验证成功！")
        print("现在可以正常运行参数优化了。")
    else:
        print("\n❌ 评估函数仍有问题，需要进一步修复。")
    
    input("\n按回车键退出...")
